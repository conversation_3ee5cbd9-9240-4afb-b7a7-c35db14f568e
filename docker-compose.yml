version: '3.8'

services:
  arangodb:
    image: arangodb/arangodb:latest
    container_name: arangodb
    ports:
      - "8529:8529"
    environment:
      ARANGO_ROOT_PASSWORD: root_password # 请替换为强密码
      ARANGO_NO_AUTH: "false" # 生产环境应为 "false"
    volumes:
      - arangodb_data:/var/lib/arangodb3
      - arangodb_apps:/var/lib/arangodb3-apps
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8529/_admin/server/version"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  arangodb_data:
  arangodb_apps:
