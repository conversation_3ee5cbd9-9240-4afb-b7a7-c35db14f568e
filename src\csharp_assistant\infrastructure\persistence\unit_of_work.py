"""
工作单元模式实现

管理数据库事务和仓储。
"""

import logging
from contextlib import contextmanager
from typing import Any, Dict, Generic, Type, TypeVar

from arango import ArangoClient
from arango.database import StandardDatabase

from ...config import get_config
from ...core.domain.entities import CodeEntity, Project

from .arangodb_repository import KnowledgeGraphManager as KnowledgeGraphRepository
from .repositories import ProjectRepository, CodeEntityRepository

logger = logging.getLogger(__name__)

T = TypeVar("T")


class UnitOfWork:
    """工作单元类，管理数据库事务和仓储"""

    def __init__(self, db: StandardDatabase):
        """
        初始化工作单元

        Args:
            db: ArangoDB 数据库连接
        """
        self.db = db
        self._repositories: Dict[Type, Any] = {}

    @property
    def projects(self) -> ProjectRepository:
        """项目仓储"""
        return self._get_repository(Project, ProjectRepository)

    @property
    def code_entities(self) -> CodeEntityRepository:
        """代码实体仓储"""
        return self._get_repository(CodeEntity, CodeEntityRepository)

    @property
    def knowledge_graph(self) -> KnowledgeGraphRepository:
        """知识图谱仓储"""
        return self._get_repository(None, KnowledgeGraphRepository)

    def _get_repository(self, entity_type: Type[T], repository_class: Type) -> Any:
        """
        获取仓储实例

        Args:
            entity_type: 实体类型
            repository_class: 仓储类

        Returns:
            Any: 仓储实例
        """
        if repository_class not in self._repositories:
            if repository_class == KnowledgeGraphRepository:
                self._repositories[repository_class] = KnowledgeGraphRepository()
            else:
                self._repositories[repository_class] = repository_class(self.db)
        return self._repositories[repository_class]

    async def begin(self) -> None:
        """
        开始事务 (ArangoDB 客户端通常隐式处理简单操作的事务)
        对于复杂事务，需要使用 AQL 事务或 ArangoDB 事务 API
        """
        logger.info("ArangoDB 事务开始 (隐式或通过 AQL)")

    async def commit(self) -> None:
        """
        提交事务
        """
        logger.info("ArangoDB 事务提交")

    async def rollback(self) -> None:
        """
        回滚事务
        """
        logger.info("ArangoDB 事务回滚")

    @contextmanager
    async def transaction(self):
        """
        事务上下文管理器

        Example:
            async with uow.transaction():
                # 在这里执行数据库操作
                project = Project(name="MyProject", path="/path/to/project")
                await uow.projects.add(project)
        """
        await self.begin()
        try:
            yield
            await self.commit()
        except Exception as e:
            await self.rollback()
            raise

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.begin()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if exc_type is not None:
            await self.rollback()
        else:
            await self.commit()


class UnitOfWorkFactory:
    """工作单元工厂"""

    def __init__(self, db: StandardDatabase):
        """
        初始化工作单元工厂

        Args:
            db: ArangoDB 数据库连接
        """
        self.db = db

    def create(self) -> UnitOfWork:
        """
        创建工作单元实例

        Returns:
            UnitOfWork: 工作单元实例
        """
        return UnitOfWork(self.db)

    @contextmanager
    def transaction(self):
        """
        创建带有事务的工作单元

        Returns:
            UnitOfWork: 带有事务的工作单元实例
        """
        uow = self.create()
        return uow.transaction()
