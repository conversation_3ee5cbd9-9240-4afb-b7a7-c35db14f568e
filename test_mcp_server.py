#!/usr/bin/env python3
"""
测试MCP服务器

这个脚本用于测试C# Assistant MCP服务器的基本功能。
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.mcp_server import MCPServer
from src.csharp_assistant.config.config import get_config


async def test_mcp_server():
    """测试MCP服务器"""
    print("🚀 开始测试C# Assistant MCP服务器...")
    
    # 设置测试环境变量
    os.environ["PROJECT_PATH"] = str(project_root)
    os.environ["PROJECT_NAME"] = "Super-Project-Assistant"
    os.environ["AI_PROVIDER"] = "ollama"
    os.environ["LANGUAGE_SERVER_ENABLED"] = "false"  # 禁用语言服务器以简化测试
    
    # 显示配置
    config = get_config()
    print(f"📁 项目路径: {config.project_path}")
    print(f"📝 项目名称: {config.project_name}")
    print(f"🤖 AI提供商: {config.ai_provider}")
    print(f"🔧 语言服务器: {'启用' if config.language_server_enabled else '禁用'}")
    print()
    
    # 创建服务器实例
    server = MCPServer()
    
    try:
        print("⏳ 正在启动服务器...")
        
        # 启动服务器（这会阻塞直到服务器停止）
        await server.start()
        
    except KeyboardInterrupt:
        print("\n⏹️  收到中断信号，正在停止服务器...")
    except Exception as e:
        print(f"❌ 服务器运行时出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🛑 正在停止服务器...")
        await server.stop()
        print("✅ 服务器已停止")


def main():
    """主函数"""
    print("=" * 60)
    print("C# Assistant MCP Server 测试")
    print("=" * 60)
    print()
    
    try:
        asyncio.run(test_mcp_server())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)
    
    print("\n🎉 测试完成！")


if __name__ == "__main__":
    main()
