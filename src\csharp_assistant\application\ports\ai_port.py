from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

class AIPort(ABC):
    """AI 服务端口定义"""
    
    @abstractmethod
    async def analyze_code(
        self, 
        code_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析代码结构"""
        pass
        
    @abstractmethod
    async def get_suggestions(
        self, 
        context: Dict[str, Any],
        query: Optional[str] = None
    ) -> List[str]:
        """获取代码建议"""
        pass
        
    @abstractmethod
    async def report_error(
        self,
        error_info: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """报告错误并获取修复建议"""
        pass
        
    @abstractmethod
    async def fix_error(
        self,
        error_info: Dict[str, Any],
        fix_suggestion: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用错误修复"""
        pass