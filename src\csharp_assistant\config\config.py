"""
配置管理

提供统一的配置管理功能，包括环境变量加载、配置验证等。
"""

import os
from pathlib import Path
from typing import Any, ClassVar, Optional

from dotenv import load_dotenv
from pydantic import ConfigDict, Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用设置"""

    # 项目配置
    project_name: str = Field(..., validation_alias="PROJECT_NAME")
    project_path: str = Field(..., validation_alias="PROJECT_PATH")
    log_level: str = Field("INFO", validation_alias="LOG_LEVEL")

    # 语言服务器配置
    language_server_enabled: bool = Field(
        True, validation_alias="LANGUAGE_SERVER_ENABLED"
    )
    language_server_host: str = Field(
        "127.0.0.1", validation_alias="LANGUAGE_SERVER_HOST"
    )
    language_server_port: int = Field(2087, validation_alias="LANGUAGE_SERVER_PORT")
    language_server_type: str = Field("roslyn", validation_alias="LANGUAGE_SERVER_TYPE")
    language_server_path: str = Field("", validation_alias="LANGUAGE_SERVER_PATH")

    # ArangoDB 配置
    arangodb_host: str = Field("localhost", validation_alias="ARANGODB_HOST")
    arangodb_port: int = Field(8529, validation_alias="ARANGODB_PORT")
    arangodb_user: str = Field("root", validation_alias="ARANGODB_USER")
    arangodb_password: str = Field(
        "root_password", validation_alias="ARANGODB_PASSWORD"
    )  # 替换为你的密码
    arangodb_database: str = Field("_system", validation_alias="ARANGODB_DATABASE")

    # LLM 配置
    llm_provider: str = Field("openai", validation_alias="LLM_PROVIDER")
    llm_model: str = Field("gpt-4-turbo", validation_alias="LLM_MODEL")
    llm_temperature: float = Field(0.7, validation_alias="LLM_TEMPERATURE")
    llm_max_tokens: int = Field(2000, validation_alias="LLM_MAX_TOKENS")
    llm_stream: bool = Field(True, validation_alias="LLM_STREAM")

    # MCP 服务配置
    mcp_host: str = Field("0.0.0.0", validation_alias="MCP_HOST")
    mcp_port: int = Field(8000, validation_alias="MCP_PORT")

    # 调试模式
    debug: bool = Field(False, validation_alias="DEBUG")

    # Pydantic 配置
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
        env_nested_delimiter="__",
    )

    @field_validator("project_path")
    def validate_project_path(cls, v: str) -> str:
        """验证项目路径是否存在"""
        if v and not Path(v).exists():
            raise ValueError(f"项目路径不存在: {v}")
        return v

    @field_validator("language_server_path")
    def validate_language_server_path(cls, v: str) -> str:
        """验证语言服务器路径是否存在"""
        if v and not Path(v).exists():
            raise ValueError(f"语言服务器路径不存在: {v}")
        return v


class Config:
    """配置管理类"""

    _instance: ClassVar[Optional["Config"]] = None
    _settings: Optional[Settings] = None

    def __new__(cls) -> "Config":
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        if self._settings is None:
            # 加载 .env 文件
            env_path = Path(".env")
            if not env_path.exists():
                # 尝试从项目根目录加载
                root_env = Path(__file__).parent.parent.parent.parent / ".env"
                if root_env.exists():
                    env_path = root_env
                else:
                    raise FileNotFoundError("未找到 .env 文件，请先复制 .env.example 并配置")

            load_dotenv(dotenv_path=env_path, override=True)
            self._settings = Settings()

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        if self._settings is None:
            self.__init__()
        return getattr(self._settings, key.lower(), default)

    def __getattr__(self, name: str) -> Any:
        """直接通过属性访问配置"""
        if self._settings is None:
            self.__init__()

        try:
            return getattr(self._settings, name)
        except AttributeError as e:
            raise AttributeError(f"'{self.__class__.__name__}' 对象没有属性 '{name}'") from e


def get_config() -> Config:
    """获取配置单例"""
    return Config()
