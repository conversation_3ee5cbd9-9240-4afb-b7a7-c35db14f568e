# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Environment variables
.env

# Logs
logs/
*.log

# Project specific
.cache/
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/

# C# specific
bin/
obj/
*.user
*.suo
*.userosscache
*.sln.docstates
*.ncrunchsolution
*.ncrunchproject
*.ncrunchproject.*
*.DotSettings.user
