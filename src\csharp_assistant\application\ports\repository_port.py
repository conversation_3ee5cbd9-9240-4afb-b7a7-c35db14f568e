from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

class KnowledgeGraphPort(ABC):
    """知识图谱端口定义"""

    @abstractmethod
    async def initialize_schema(self):
        """初始化知识图谱模式"""
        pass

    @abstractmethod
    async def update_from_analysis(
        self,
        project_name: str,
        analysis: Dict[str, Any]
    ):
        """根据代码分析结果更新知识图谱"""
        pass

    @abstractmethod
    async def query_context(
        self,
        query: str,
        limit: int = 5
    ) -> List[Dict]:
        """查询相关知识图谱上下文"""
        pass

    @abstractmethod
    def close(self):
        """关闭数据库连接"""
        pass