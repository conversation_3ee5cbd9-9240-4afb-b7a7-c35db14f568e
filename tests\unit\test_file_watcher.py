"""
文件监控器单元测试
"""

import pytest
import asyncio
import os
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path

from src.csharp_assistant.infrastructure.adapters.file_watcher import FileWatcher, FileChangeHandler


class TestFileChangeHandler:
    """文件变更处理器测试类"""

    @pytest.fixture
    def callback_mock(self):
        """模拟回调函数"""
        return AsyncMock()

    @pytest.fixture
    def handler(self, callback_mock):
        """创建文件变更处理器实例"""
        return FileChangeHandler(callback=callback_mock)

    def test_init(self, handler, callback_mock):
        """测试初始化"""
        assert handler.callback == callback_mock
        assert handler.debounce_delay == 2.0
        assert len(handler.pending_files) == 0
        assert len(handler.debounce_tasks) == 0

    def test_should_process_file_cs_file(self, handler):
        """测试应该处理C#文件"""
        assert handler._should_process_file("test.cs") is True
        assert handler._should_process_file("project.csproj") is True
        assert handler._should_process_file("solution.sln") is True

    def test_should_process_file_ignore_patterns(self, handler):
        """测试忽略特定模式的文件"""
        assert handler._should_process_file("bin/debug/test.cs") is False
        assert handler._should_process_file("obj/release/test.cs") is False
        assert handler._should_process_file(".vs/config.json") is False
        assert handler._should_process_file(".git/config") is False
        assert handler._should_process_file("test.tmp") is False
        assert handler._should_process_file("test~") is False

    def test_should_process_file_non_cs_files(self, handler):
        """测试不应该处理非C#文件"""
        assert handler._should_process_file("readme.txt") is False
        assert handler._should_process_file("config.json") is False
        assert handler._should_process_file("script.py") is False

    @pytest.mark.asyncio
    async def test_debounced_process_file(self, handler, callback_mock):
        """测试防抖处理文件"""
        file_path = "test.cs"
        handler.pending_files.add(file_path)
        
        # 模拟短延迟以加快测试
        handler.debounce_delay = 0.1
        
        await handler._debounced_process_file(file_path, "modified")
        
        # 验证回调被调用
        callback_mock.assert_called_once_with(file_path, "modified")
        assert file_path not in handler.pending_files

    def test_schedule_file_processing_cs_file(self, handler):
        """测试调度C#文件处理"""
        file_path = "test.cs"
        
        with patch('asyncio.create_task') as mock_create_task:
            mock_task = MagicMock()
            mock_create_task.return_value = mock_task
            
            handler._schedule_file_processing(file_path, "modified")
            
            assert file_path in handler.pending_files
            assert file_path in handler.debounce_tasks
            mock_create_task.assert_called_once()

    def test_schedule_file_processing_ignore_file(self, handler):
        """测试调度忽略文件处理"""
        file_path = "readme.txt"
        
        with patch('asyncio.create_task') as mock_create_task:
            handler._schedule_file_processing(file_path, "modified")
            
            assert file_path not in handler.pending_files
            assert file_path not in handler.debounce_tasks
            mock_create_task.assert_not_called()

    def test_schedule_file_processing_cancel_previous(self, handler):
        """测试调度文件处理时取消之前的任务"""
        file_path = "test.cs"
        
        # 创建一个模拟的之前任务
        previous_task = MagicMock()
        handler.debounce_tasks[file_path] = previous_task
        
        with patch('asyncio.create_task') as mock_create_task:
            mock_task = MagicMock()
            mock_create_task.return_value = mock_task
            
            handler._schedule_file_processing(file_path, "modified")
            
            # 验证之前的任务被取消
            previous_task.cancel.assert_called_once()
            assert handler.debounce_tasks[file_path] == mock_task

    def test_on_modified(self, handler):
        """测试文件修改事件"""
        event = MagicMock()
        event.is_directory = False
        event.src_path = "test.cs"
        
        with patch.object(handler, '_schedule_file_processing') as mock_schedule:
            handler.on_modified(event)
            mock_schedule.assert_called_once_with("test.cs", "modified")

    def test_on_created(self, handler):
        """测试文件创建事件"""
        event = MagicMock()
        event.is_directory = False
        event.src_path = "test.cs"
        
        with patch.object(handler, '_schedule_file_processing') as mock_schedule:
            handler.on_created(event)
            mock_schedule.assert_called_once_with("test.cs", "created")

    def test_on_deleted(self, handler):
        """测试文件删除事件"""
        event = MagicMock()
        event.is_directory = False
        event.src_path = "test.cs"
        
        with patch.object(handler, '_schedule_file_processing') as mock_schedule:
            handler.on_deleted(event)
            mock_schedule.assert_called_once_with("test.cs", "deleted")

    def test_on_directory_events_ignored(self, handler):
        """测试目录事件被忽略"""
        event = MagicMock()
        event.is_directory = True
        event.src_path = "test_directory"
        
        with patch.object(handler, '_schedule_file_processing') as mock_schedule:
            handler.on_modified(event)
            handler.on_created(event)
            handler.on_deleted(event)
            mock_schedule.assert_not_called()


class TestFileWatcher:
    """文件监控器测试类"""

    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        config = MagicMock()
        config.project_path = "/test/project"
        return config

    @pytest.fixture
    def callback_mock(self):
        """模拟回调函数"""
        return AsyncMock()

    @pytest.fixture
    def file_watcher(self, mock_config, callback_mock):
        """创建文件监控器实例"""
        with patch('src.csharp_assistant.infrastructure.adapters.file_watcher.get_config', return_value=mock_config):
            return FileWatcher(change_callback=callback_mock)

    def test_init(self, file_watcher, mock_config, callback_mock):
        """测试初始化"""
        assert file_watcher.config == mock_config
        assert file_watcher.observer is not None
        assert file_watcher.event_handler.callback == callback_mock
        assert not file_watcher.is_running

    @pytest.mark.asyncio
    async def test_start_already_running(self, file_watcher):
        """测试已在运行时启动"""
        file_watcher.is_running = True
        
        with patch.object(file_watcher.observer, 'schedule') as mock_schedule:
            await file_watcher.start()
            mock_schedule.assert_not_called()

    @pytest.mark.asyncio
    async def test_start_path_not_exists(self, file_watcher):
        """测试项目路径不存在时启动"""
        with patch('os.path.exists', return_value=False):
            await file_watcher.start()
            assert not file_watcher.is_running

    @pytest.mark.asyncio
    async def test_start_success(self, file_watcher):
        """测试成功启动"""
        with patch('os.path.exists', return_value=True):
            with patch.object(file_watcher.observer, 'schedule') as mock_schedule:
                with patch.object(file_watcher.observer, 'start') as mock_start:
                    await file_watcher.start()
                    
                    mock_schedule.assert_called_once()
                    mock_start.assert_called_once()
                    assert file_watcher.is_running

    @pytest.mark.asyncio
    async def test_start_exception(self, file_watcher):
        """测试启动时异常"""
        with patch('os.path.exists', return_value=True):
            with patch.object(file_watcher.observer, 'schedule', side_effect=Exception("测试异常")):
                await file_watcher.start()
                assert not file_watcher.is_running

    @pytest.mark.asyncio
    async def test_stop_not_running(self, file_watcher):
        """测试未运行时停止"""
        with patch.object(file_watcher.observer, 'stop') as mock_stop:
            await file_watcher.stop()
            mock_stop.assert_not_called()

    @pytest.mark.asyncio
    async def test_stop_success(self, file_watcher):
        """测试成功停止"""
        file_watcher.is_running = True
        
        # 模拟一些待处理的任务
        task1 = MagicMock()
        task1.done.return_value = False
        task2 = MagicMock()
        task2.done.return_value = True
        
        file_watcher.event_handler.debounce_tasks = {
            "file1.cs": task1,
            "file2.cs": task2
        }
        
        with patch.object(file_watcher.observer, 'stop') as mock_stop:
            with patch.object(file_watcher.observer, 'join') as mock_join:
                await file_watcher.stop()
                
                # 验证未完成的任务被取消
                task1.cancel.assert_called_once()
                task2.cancel.assert_not_called()
                
                mock_stop.assert_called_once()
                mock_join.assert_called_once()
                assert not file_watcher.is_running

    def test_set_change_callback(self, file_watcher):
        """测试设置变更回调"""
        new_callback = AsyncMock()
        file_watcher.set_change_callback(new_callback)
        assert file_watcher.event_handler.callback == new_callback

    @pytest.mark.asyncio
    async def test_wait_for_events(self, file_watcher):
        """测试等待事件"""
        with patch.object(file_watcher, 'start') as mock_start:
            with patch('asyncio.sleep', side_effect=KeyboardInterrupt()):
                with patch.object(file_watcher, 'stop') as mock_stop:
                    await file_watcher.wait_for_events()
                    
                    mock_start.assert_called_once()
                    mock_stop.assert_called_once()
