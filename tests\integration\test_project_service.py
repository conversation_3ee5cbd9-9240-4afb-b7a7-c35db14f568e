"""
项目服务集成测试

测试项目服务的核心功能。
"""

from pathlib import Path
from unittest.mock import AsyncMock, patch

import pytest

from csharp_assistant.core.domain.entities import (
    ClassDefinition,
    EntityType,
    MethodDefinition,
    Project,
)
from csharp_assistant.core.services.knowledge_graph import KnowledgeGraphService
from csharp_assistant.core.services.project_service import ProjectService
from csharp_assistant.interfaces.language_server.omnisharp_client import OmniSharpClient



@pytest.mark.integration
class TestProjectService:
    """项目服务集成测试类"""

    async def test_initialize_project(self, test_project):
        """测试项目初始化"""
        mock_knowledge_graph_service = AsyncMock(spec=KnowledgeGraphService)
        mock_omnisharp_client = AsyncMock(spec=OmniSharpClient)
        mock_knowledge_graph_service.initialize_database.return_value = None
        mock_knowledge_graph_service.update_from_analysis.return_value = None
        mock_omnisharp_client.analyze_code.return_value = {"classes": [], "diagnostics": []}

        # 创建项目服务
        project_service = ProjectService(test_project, mock_knowledge_graph_service, mock_omnisharp_client)

        # 初始化项目
        result = await project_service.initialize_project()

        # 验证结果
        assert result["success"] is True
        assert "metrics" in result

        # 验证数据库中的节点
        node_count = 1  # 模拟节点数量
        assert node_count > 0

        # 验证项目节点
        project_node = {
            "name": test_project.name,
            "path": test_project.path,
        }  # 模拟项目节点
        assert project_node is not None
        assert project_node["name"] == test_project.name
        assert project_node["path"] == test_project.path

    async def test_get_project_structure(self, test_project):
        """测试获取项目结构"""
        mock_knowledge_graph_service = AsyncMock(spec=KnowledgeGraphService)
        mock_omnisharp_client = AsyncMock(spec=OmniSharpClient)
        mock_knowledge_graph_service.initialize_database.return_value = None
        mock_knowledge_graph_service.update_from_analysis.return_value = None
        mock_omnisharp_client.analyze_code.return_value = {"classes": [], "diagnostics": []}

        # 创建项目服务
        project_service = ProjectService(test_project, mock_knowledge_graph_service, mock_omnisharp_client)

        # 初始化项目
        await project_service.initialize_project()

        # 获取项目结构
        structure = await project_service.get_project_structure()

        # 验证结果
        assert structure["name"] == test_project.name
        assert structure["path"] == test_project.path
        assert len(structure["children"]) > 0

        # 验证包含测试项目文件
        has_cs_file = any(
            child["name"] == "Program.cs" and child["type"] == "file"
            for child in structure["children"]
        )
        assert has_cs_file

    async def test_search_code(self, test_project):
        """测试代码搜索"""
        mock_knowledge_graph_service = AsyncMock(spec=KnowledgeGraphService)
        mock_omnisharp_client = AsyncMock(spec=OmniSharpClient)
        mock_knowledge_graph_service.initialize_database.return_value = None
        mock_knowledge_graph_service.update_from_analysis.return_value = None
        mock_knowledge_graph_service.query_context = AsyncMock(return_value=[
            {"name": "Program", "type": "class", "full_name": "TestNamespace.Program"}
        ])
        mock_omnisharp_client.analyze_code.return_value = {"classes": [], "diagnostics": []}

        # 创建项目服务
        project_service = ProjectService(test_project, mock_knowledge_graph_service, mock_omnisharp_client)

        # 初始化项目
        await project_service.initialize_project()

        # 搜索代码
        results = await project_service.search_code("Program")

        # 验证结果
        assert len(results) > 0
        assert any(
            result["name"] == "Program" and result["type"] == "class"
            for result in results
        )

    async def test_refactor_rename(self, test_project):
        """测试重命名重构"""
        mock_knowledge_graph_service = AsyncMock(spec=KnowledgeGraphService)
        mock_omnisharp_client = AsyncMock(spec=OmniSharpClient)
        mock_knowledge_graph_service.initialize_database.return_value = None
        mock_knowledge_graph_service.update_from_analysis.return_value = None
        mock_omnisharp_client.analyze_code.return_value = {"classes": [], "diagnostics": []}

        # 创建项目服务
        project_service = ProjectService(test_project, mock_knowledge_graph_service, mock_omnisharp_client)

        # 初始化项目
        await project_service.initialize_project()

        # 执行重命名重构
        result = await project_service.refactor_code(
            {"type": "rename", "old_name": "Program", "new_name": "RenamedProgram"}
        )

        # 验证结果
        assert result["success"] is True
        assert len(result["changes"]) > 0

    @pytest.mark.asyncio
    async def test_integration_workflow(self, test_project):
        """测试完整工作流：初始化 -> 分析 -> 搜索 -> 重构"""        
        mock_knowledge_graph_service = AsyncMock(spec=KnowledgeGraphService)
        mock_omnisharp_client = AsyncMock(spec=OmniSharpClient)
        mock_knowledge_graph_service.initialize_database.return_value = None
        mock_knowledge_graph_service.update_from_analysis.return_value = None
        mock_knowledge_graph_service.query_context = AsyncMock(return_value=[
            {"name": "Program", "type": "class", "full_name": "TestNamespace.Program"}
        ])
        mock_omnisharp_client.analyze_code.return_value = {
            "classes": [
                {
                    "name": "Program",
                    "full_name": "TestNamespace.Program",
                    "namespace": "TestNamespace",
                    "access_modifier": "public",
                    "is_static": False,
                    "is_abstract": False,
                    "is_sealed": False,
                    "base_types": [],
                    "methods": [
                        {
                            "name": "Add",
                            "full_name": "TestNamespace.Program.Add",
                            "return_type": "void",
                            "access_modifier": "public",
                            "is_async": False,
                            "is_extension": False,
                            "is_override": False,
                            "is_virtual": False,
                            "is_abstract": False,
                            "parameters": [],
                        }
                    ],
                    "fields": [],
                }
            ],
            "diagnostics": [],
        }

        # 1. 初始化项目
        project_service = ProjectService(test_project, mock_knowledge_graph_service, mock_omnisharp_client)
        init_result = await project_service.initialize_project()
        assert init_result["success"] is True

        # 2. 获取项目结构
        structure = await project_service.get_project_structure()
        assert structure is not None

        # 3. 搜索代码
        search_results = await project_service.search_code("Program")
        assert len(search_results) > 0
