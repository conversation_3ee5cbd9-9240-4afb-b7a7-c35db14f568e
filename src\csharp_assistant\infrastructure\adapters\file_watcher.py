"""
文件系统监控
"""

import asyncio
import logging
import os
from pathlib import Path
from typing import Callable, Optional, Set
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileModifiedEvent, FileCreatedEvent, FileDeletedEvent

from ...config.config import get_config

logger = logging.getLogger(__name__)


class FileChangeHandler(FileSystemEventHandler):
    """文件变更处理器"""

    def __init__(self, callback: Optional[Callable] = None):
        super().__init__()
        self.callback = callback
        self.debounce_delay = 2.0  # 防抖延迟（秒）
        self.pending_files: Set[str] = set()
        self.debounce_tasks = {}

    def _should_process_file(self, file_path: str) -> bool:
        """判断是否应该处理该文件"""
        path = Path(file_path)

        # 只处理C#相关文件
        if path.suffix not in ['.cs', '.csproj', '.sln']:
            return False

        # 忽略临时文件和编译输出
        ignore_patterns = [
            'bin/', 'obj/', '.vs/', '.git/', '__pycache__/',
            '.tmp', '.temp', '~', '.swp', '.bak'
        ]

        file_path_str = str(path)
        for pattern in ignore_patterns:
            if pattern in file_path_str:
                return False

        return True

    async def _debounced_process_file(self, file_path: str, event_type: str):
        """防抖处理文件变更"""
        try:
            # 等待防抖延迟
            await asyncio.sleep(self.debounce_delay)

            # 检查文件是否仍在待处理列表中
            if file_path in self.pending_files:
                self.pending_files.discard(file_path)

                if self.callback:
                    await self.callback(file_path, event_type)

        except Exception as e:
            logger.error(f"处理文件变更时出错: {e}", exc_info=True)
        finally:
            # 清理任务
            if file_path in self.debounce_tasks:
                del self.debounce_tasks[file_path]

    def _schedule_file_processing(self, file_path: str, event_type: str):
        """调度文件处理"""
        if not self._should_process_file(file_path):
            return

        # 取消之前的任务
        if file_path in self.debounce_tasks:
            self.debounce_tasks[file_path].cancel()

        # 添加到待处理列表
        self.pending_files.add(file_path)

        # 创建新的防抖任务
        task = asyncio.create_task(self._debounced_process_file(file_path, event_type))
        self.debounce_tasks[file_path] = task

    def on_modified(self, event):
        """文件修改事件"""
        if not event.is_directory:
            logger.debug(f"文件已修改: {event.src_path}")
            self._schedule_file_processing(event.src_path, "modified")

    def on_created(self, event):
        """文件创建事件"""
        if not event.is_directory:
            logger.debug(f"文件已创建: {event.src_path}")
            self._schedule_file_processing(event.src_path, "created")

    def on_deleted(self, event):
        """文件删除事件"""
        if not event.is_directory:
            logger.debug(f"文件已删除: {event.src_path}")
            self._schedule_file_processing(event.src_path, "deleted")


class FileWatcher:
    """文件监控器"""

    def __init__(self, change_callback: Optional[Callable] = None):
        self.config = get_config()
        self.observer = Observer()
        self.event_handler = FileChangeHandler(callback=change_callback)
        self.is_running = False

    async def start(self):
        """启动监控"""
        if self.is_running:
            logger.warning("文件监控已在运行")
            return

        if not self.config.project_path or not os.path.exists(self.config.project_path):
            logger.error(f"项目路径不存在: {self.config.project_path}")
            return

        try:
            self.observer.schedule(
                self.event_handler, self.config.project_path, recursive=True
            )
            self.observer.start()
            self.is_running = True
            logger.info(f"开始监控目录: {self.config.project_path}")

        except Exception as e:
            logger.error(f"启动文件监控失败: {e}", exc_info=True)
            self.is_running = False

    async def stop(self):
        """停止监控"""
        if not self.is_running:
            return

        try:
            # 取消所有待处理的任务
            for task in self.event_handler.debounce_tasks.values():
                if not task.done():
                    task.cancel()

            self.observer.stop()
            self.observer.join()
            self.is_running = False
            logger.info("文件监控已停止")

        except Exception as e:
            logger.error(f"停止文件监控时出错: {e}", exc_info=True)

    def set_change_callback(self, callback: Callable):
        """设置文件变更回调函数"""
        self.event_handler.callback = callback

    async def wait_for_events(self):
        """等待文件事件（用于测试或独立运行）"""
        if not self.is_running:
            await self.start()

        try:
            while self.is_running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到中断信号，停止文件监控")
        finally:
            await self.stop()
