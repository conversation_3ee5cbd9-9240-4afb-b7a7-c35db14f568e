"""
文件系统监控
"""

import asyncio
import logging
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from ...config.config import get_config

logger = logging.getLogger(__name__)


class FileChangeHandler(FileSystemEventHandler):
    """文件变更处理器"""

    def on_modified(self, event):
        if not event.is_directory:
            logger.info(f"File modified: {event.src_path}")


class FileWatcher:
    """文件监控器"""

    def __init__(self):
        self.config = get_config()
        self.observer = Observer()
        self.event_handler = FileChangeHandler()

    async def start(self):
        """启动监控"""
        self.observer.schedule(
            self.event_handler, self.config.project_path, recursive=True
        )
        self.observer.start()
        logger.info(f"开始监控目录: {self.config.project_path}")
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            self.observer.stop()
        self.observer.join()

    async def stop(self):
        """停止监控"""
        self.observer.stop()
        self.observer.join()
        logger.info("文件监控已停止")
