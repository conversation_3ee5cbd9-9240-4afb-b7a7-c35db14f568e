"""
知识图谱管理器
"""

from typing import Any, Dict, List, Optional
from arango import ArangoClient
from ...application.ports.repository_port import KnowledgeGraphPort
from ...config.config import get_config


class KnowledgeGraphManager(KnowledgeGraphPort):
    """知识图谱管理器"""

    def __init__(self):
        config = get_config()
        self.client = ArangoClient(hosts=f"http://{config.arangodb_host}:{config.arangodb_port}")
        self.db = self.client.db(
            config.arangodb_database,
            username=config.arangodb_user,
            password=config.arangodb_password,
        )
        self.graph = self.db.graph("csharp_knowledge_graph")

    async def initialize_schema(self):
        """初始化知识图谱模式"""
        # Create vertex collections
        for collection_name in ["Project", "File", "Class", "Method", "Parameter", "Field"]:
            if not self.db.has_collection(collection_name):
                self.db.create_collection(collection_name)

        # Create edge collections and define graph
        edge_definitions = [
            {"edge_collection": "CONTAINS_FILE", "from_vertex_collections": ["Project"], "to_vertex_collections": ["File"]},
            {"edge_collection": "CONTAINS_CLASS", "from_vertex_collections": ["File"], "to_vertex_collections": ["Class"]},
            {"edge_collection": "CONTAINS_METHOD", "from_vertex_collections": ["Class"], "to_vertex_collections": ["Method"]},
            {"edge_collection": "HAS_PARAMETER", "from_vertex_collections": ["Method"], "to_vertex_collections": ["Parameter"]},
            {"edge_collection": "CALLS", "from_vertex_collections": ["Method"], "to_vertex_collections": ["Method"]},
            {"edge_collection": "HAS_FIELD", "from_vertex_collections": ["Class"], "to_vertex_collections": ["Field"]},
        ]
        
        if not self.db.has_graph("csharp_knowledge_graph"):
            self.db.create_graph("csharp_knowledge_graph", edge_definitions=edge_definitions)
        else:
            # Update edge definitions if graph already exists
            existing_graph = self.db.graph("csharp_knowledge_graph")
            for edge_def in edge_definitions:
                if not existing_graph.has_edge_definition(edge_def["edge_collection"]):
                    existing_graph.create_edge_definition(**edge_def)

        # Create indexes
        # For Project, File, Class, Method, Field, Parameter, we'll use hash indexes on 'name' or 'path'
        # For full-text search, ArangoDB uses ArangoSearch Views.
        # This is a basic example, a real implementation would involve more complex AQL and view definitions.
        # For simplicity, we'll rely on direct AQL queries for now.
        
        # Ensure persistent indexes for common lookups
        for col_name, field_name in [
            ("Project", "name"), ("File", "path"), ("Class", "name"), 
            ("Method", "full_name"), ("Field", "name"), ("Parameter", "name")
        ]:
            collection = self.db.collection(col_name);
            if not any(idx["type"] == "persistent" and field_name in idx["fields"] for idx in collection.indexes()):
                collection.add_hash_index(fields=[field_name], unique=False)


    async def update_from_analysis(
        self, 
        project_name: str,
        analysis: Dict[str, Any]
    ):
        """根据代码分析结果更新知识图谱"""
        # Get collections
        projects_col = self.db.collection("Project")
        files_col = self.db.collection("File")
        classes_col = self.db.collection("Class")
        methods_col = self.db.collection("Method")
        parameters_col = self.db.collection("Parameter")
        fields_col = self.db.collection("Field")

        # Get edge collections
        contains_file_edge = self.graph.edge_collection("CONTAINS_FILE")
        contains_class_edge = self.graph.edge_collection("CONTAINS_CLASS")
        contains_method_edge = self.graph.edge_collection("CONTAINS_METHOD")
        has_parameter_edge = self.graph.edge_collection("HAS_PARAMETER")
        calls_edge = self.graph.edge_collection("CALLS")
        has_field_edge = self.graph.edge_collection("HAS_FIELD")

        # Create or update Project node
        project_key = project_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_") # Simple key generation
        try:
            project_doc = projects_col.insert({"_key": project_key, "name": project_name}, overwrite=True)
        except Exception as e:
            print(f"Error inserting project: {e}")
            return

        for file_data in analysis.get("files", []):
            file_path = file_data.get("path")
            if not file_path:
                continue

            file_key = file_path.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
            file_doc = files_col.insert({"_key": file_key, "path": file_path}, overwrite=True)
            contains_file_edge.insert({"_from": project_doc["_id"], "_to": file_doc["_id"]})

            for class_data in file_data.get("classes", []):
                class_name = class_data.get("name")
                class_namespace = class_data.get("namespace")
                if not class_name:
                    continue

                class_full_name = f"{class_namespace}.{class_name}" if class_namespace else class_name
                class_key = class_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                class_doc = classes_col.insert({"_key": class_key, "name": class_name, "namespace": class_namespace, "full_name": class_full_name}, overwrite=True)
                contains_class_edge.insert({"_from": file_doc["_id"], "_to": class_doc["_id"]})

                for method_data in class_data.get("methods", []):
                    method_name = method_data.get("name")
                    method_return_type = method_data.get("return_type")
                    if not method_name:
                        continue

                    method_full_name = f"{class_full_name}.{method_name}"
                    method_key = method_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                    method_doc = methods_col.insert({"_key": method_key, "name": method_name, "return_type": method_return_type, "full_name": method_full_name}, overwrite=True)
                    contains_method_edge.insert({"_from": class_doc["_id"], "_to": method_doc["_id"]})

                    for param_data in method_data.get("parameters", []):
                        param_name = param_data.get("name")
                        param_type = param_data.get("type")
                        if not param_name:
                            continue
                        param_key = f"{method_full_name}.{param_name}".replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                        param_doc = parameters_col.insert({"_key": param_key, "name": param_name, "type": param_type}, overwrite=True)
                        has_parameter_edge.insert({"_from": method_doc["_id"], "_to": param_doc["_id"]})

                    for called_method_full_name in method_data.get("calls", []):
                        # Find or create callee method
                        callee_key = called_method_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                        callee_doc = methods_col.insert({"_key": callee_key, "full_name": called_method_full_name}, overwrite=True)
                        calls_edge.insert({"_from": method_doc["_id"], "_to": callee_doc["_id"]})

                for field_data in class_data.get("fields", []):
                    field_name = field_data.get("name")
                    field_type = field_data.get("type")
                    if not field_name:
                        continue

                    field_full_name = f"{class_full_name}.{field_name}"
                    field_key = field_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                    field_doc = fields_col.insert({"_key": field_key, "name": field_name, "type": field_type}, overwrite=True)
                    has_field_edge.insert({"_from": class_doc["_id"], "_to": field_doc["_id"]})

    async def query_context(
        self, 
        query: str,
        limit: int = 5
    ) -> List[Dict]:
        """查询相关知识图谱上下文"""
        # This is a simplified example. For robust full-text search, ArangoSearch Views are recommended.
        # Here, we'll do a basic AQL search on 'name' and 'full_name' fields.
        aql_query = f"""
        FOR node IN @@collection
            FILTER LIKE(LOWER(node.name), LOWER(@query_param), true) OR LIKE(LOWER(node.full_name), LOWER(@query_param), true)
            LIMIT @limit_param
            RETURN node
        """
        
        results = []
        # Search in Class and Method collections
        for collection_name in ["Class", "Method"]:
            cursor = self.db.aql.execute(
                aql_query,
                bind_vars={
"@collection": collection_name, "query_param": f"%{query}%", "limit_param": limit}
            )
            results.extend([doc for doc in cursor])
        
        return results

    def close(self):
        """关闭数据库连接"""
        # ArangoDB client doesn't require explicit close for HTTP connections
        pass