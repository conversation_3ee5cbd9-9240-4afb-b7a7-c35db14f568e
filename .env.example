# 项目配置
PROJECT_NAME=MyCSharpProject
PROJECT_PATH=C:\\path\\to\\your\\solution.sln
LOG_LEVEL=INFO

# ======================
# Language Server 配置
# ======================

# 是否启用语言服务器
LANGUAGE_SERVER_ENABLED=true
# 语言服务器监听地址
LANGUAGE_SERVER_HOST=127.0.0.1
# 语言服务器端口
LANGUAGE_SERVER_PORT=2087
# 语言服务器类型 (python-lsp-server, omnisharp, roslyn)
LANGUAGE_SERVER_TYPE=python-lsp-server
# 语言服务器可执行文件路径（如果不在PATH中）
LANGUAGE_SERVER_PATH=C:\\path\\to\\omnisharp\\OmniSharp.exe

# ======================
# 大模型配置
# ======================

# 默认模型供应商 (openai, azure, anthropic, local, etc.)
LLM_PROVIDER=openai
# 默认模型名称
LLM_MODEL=gpt-4-turbo
# 生成文本的随机性 (0.0-2.0)
LLM_TEMPERATURE=0.7
# 生成的最大token数
LLM_MAX_TOKENS=2000
# 是否流式输出
LLM_STREAM=true

# ======================
# OpenAI 配置
# ======================
OPENAI_API_KEY=your_openai_api_key_here
# 可选：自定义API端点（用于代理或自托管）
# OPENAI_API_BASE=https://api.openai.com/v1

# ======================
# Azure OpenAI 配置
# ======================
# AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
# AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
# AZURE_OPENAI_DEPLOYMENT=your_deployment_name
# AZURE_OPENAI_API_VERSION=2023-05-15

# ======================
# Anthropic 配置
# ======================
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# ANTHROPIC_MODEL=claude-3-opus-20240229

# ======================
# 本地模型配置
# ======================
# 本地模型路径或标识符
# LOCAL_MODEL_NAME=TheBloke/Mistral-7B-Instruct-v0.1-GGUF
# 本地服务器URL（如使用ollama、vllm等）
# LOCAL_API_BASE=http://localhost:8000/v1
# 上下文窗口大小
# LOCAL_CONTEXT_WINDOW=4096

# ======================
# ArangoDB 配置
# ======================
ARANGODB_HOST=localhost
ARANGODB_PORT=8529
ARANGODB_USER=root
ARANGODB_PASSWORD=12345678 # 替换为你的密码
ARANGODB_DATABASE=_system # 替换为你的数据库名

# 其他配置
DEBUG=True
