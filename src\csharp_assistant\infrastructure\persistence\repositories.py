"""
仓储实现

实现领域模型的持久化接口。
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from arango import ArangoClient
from arango.collection import StandardCollection
from arango.database import StandardDatabase

from ...config import get_config
from ...core.domain.entities import (
    ClassDefinition,
    CodeEntity,
    EntityType,
    MethodDefinition,
    Project,
    PropertyDefinition,
)
from ...core.domain.value_objects import CodeAnalysisResult, CodeChange

logger = logging.getLogger(__name__)

T = TypeVar("T")


class BaseRepository(Generic[T], ABC):
    """仓储基类"""

    def __init__(self, db: StandardDatabase, collection_name: str):
        """
        初始化仓储

        Args:
            db: ArangoDB 数据库连接
            collection_name: 集合名称
        """
        self.db = db
        self.collection: StandardCollection = db.collection(collection_name)

    @abstractmethod
    def _to_entity(self, doc: Dict[str, Any]) -> T:
        """将 ArangoDB 文档转换为领域实体"""
        pass

    @abstractmethod
    def _to_doc(self, entity: T) -> Dict[str, Any]:
        """将领域实体转换为 ArangoDB 文档"""
        pass

    async def get(self, key: str) -> Optional[T]:
        """
        根据文档键获取实体

        Args:
            key: 文档键 (_key)

        Returns:
            Optional[T]: 找到的实体，如果不存在则返回None
        """
        try:
            doc = self.collection.get(key)
            if doc:
                return self._to_entity(doc)
            return None
        except Exception as e:
            logger.error(f"获取实体时出错: {e}", exc_info=True)
            raise

    async def add(self, entity: T) -> None:
        """
        添加实体

        Args:
            entity: 要添加的实体
        """
        try:
            doc = self._to_doc(entity)
            self.collection.insert(doc)
        except Exception as e:
            logger.error(f"添加实体时出错: {e}", exc_info=True)
            raise

    async def update(self, entity: T) -> None:
        """
        更新实体

        Args:
            entity: 要更新的实体
        """
        try:
            doc = self._to_doc(entity)
            self.collection.update(doc)
        except Exception as e:
            logger.error(f"更新实体时出错: {e}", exc_info=True)
            raise

    async def delete(self, key: str) -> None:
        """
        删除实体

        Args:
            key: 要删除的实体键 (_key)
        """
        try:
            self.collection.delete(key)
        except Exception as e:
            logger.error(f"删除实体时出错: {e}", exc_info=True)
            raise


class ProjectRepository(BaseRepository[Project]):
    """项目仓储"""

    def __init__(self, db: StandardDatabase):
        super().__init__(db, "projects")

    def _to_entity(self, doc: Dict[str, Any]) -> Project:
        """将 ArangoDB 文档转换为项目实体"""
        return Project(
            id=doc["_key"],
            name=doc["name"],
            path=doc["path"],
            solution_path=doc.get("solution_path"),
        )

    def _to_doc(self, entity: Project) -> Dict[str, Any]:
        """将项目实体转换为 ArangoDB 文档"""
        return {
            "_key": str(entity.id),
            "name": entity.name,
            "path": entity.path,
            "solution_path": entity.solution_path,
        }

    async def get_by_name(self, name: str) -> Optional[Project]:
        """
        根据名称获取项目

        Args:
            name: 项目名称

        Returns:
            Optional[Project]: 找到的项目，如果不存在则返回None
        """
        try:
            aql = "FOR p IN projects FILTER p.name == @name RETURN p"
            cursor = self.db.aql.execute(aql, bind_vars={"name": name})
            async for doc in cursor:
                return self._to_entity(doc)
            return None
        except Exception as e:
            logger.error(f"根据名称获取项目时出错: {e}", exc_info=True)
            raise


class CodeEntityRepository(BaseRepository[CodeEntity]):
    """代码实体仓储"""

    def __init__(self, db: StandardDatabase):
        super().__init__(db, "code_entities")

    def _to_entity(self, doc: Dict[str, Any]) -> CodeEntity:
        """将 ArangoDB 文档转换为代码实体"""
        entity_type = doc["type"]

        if entity_type == EntityType.CLASS.value:
            return ClassDefinition(
                id=doc["_key"],
                name=doc["name"],
                full_name=doc["full_name"],
                access_modifier=doc.get("access_modifier", "private"),
                namespace=doc.get("namespace", ""),
                is_static=doc.get("is_static", False),
                is_abstract=doc.get("is_abstract", False),
                is_sealed=doc.get("is_sealed", False),
                base_types=doc.get("base_types", []),
            )
        elif entity_type == EntityType.METHOD.value:
            return MethodDefinition(
                id=doc["_key"],
                name=doc["name"],
                full_name=doc["full_name"],
                access_modifier=doc.get("access_modifier", "private"),
                return_type=doc.get("return_type", "void"),
                is_async=doc.get("is_async", False),
                is_extension=doc.get("is_extension", False),
                is_override=doc.get("is_override", False),
                is_virtual=doc.get("is_virtual", False),
                is_abstract=doc.get("is_abstract", False),
                parameters=doc.get("parameters", []),
            )
        elif entity_type == EntityType.PROPERTY.value:
            return PropertyDefinition(
                id=doc["_key"],
                name=doc["name"],
                full_name=doc["full_name"],
                access_modifier=doc.get("access_modifier", "private"),
                property_type=doc.get("property_type", "object"),
                has_getter=doc.get("has_getter", True),
                has_setter=doc.get("has_setter", False),
                is_auto_property=doc.get("is_auto_property", True),
            )
        else:
            return CodeEntity(
                id=doc["_key"],
                name=doc["name"],
                full_name=doc["full_name"],
                type=EntityType(entity_type),
                access_modifier=doc.get("access_modifier", "private"),
            )

    def _to_doc(self, entity: CodeEntity) -> Dict[str, Any]:
        """将代码实体转换为 ArangoDB 文档"""
        doc_data = {
            "_key": str(entity.id),
            "name": entity.name,
            "full_name": entity.full_name,
            "type": entity.type.value,
            "access_modifier": entity.access_modifier.value,
            "documentation": entity.documentation,
            "metadata": entity.metadata,
            "created_at": entity.created_at.isoformat(),
            "updated_at": entity.updated_at.isoformat(),
        }

        # 添加特定类型的属性
        if isinstance(entity, ClassDefinition):
            doc_data.update(
                {
                    "namespace": entity.namespace,
                    "is_static": entity.is_static,
                    "is_abstract": entity.is_abstract,
                    "is_sealed": entity.is_sealed,
                    "base_types": entity.base_types,
                }
            )
        elif isinstance(entity, MethodDefinition):
            doc_data.update(
                {
                    "return_type": entity.return_type,
                    "is_async": entity.is_async,
                    "is_extension": entity.is_extension,
                    "is_override": entity.is_override,
                    "is_virtual": entity.is_virtual,
                    "is_abstract": entity.is_abstract,
                    "parameters": entity.parameters,
                }
            )
        elif isinstance(entity, PropertyDefinition):
            doc_data.update(
                {
                    "property_type": entity.property_type,
                    "has_getter": entity.has_getter,
                    "has_setter": entity.has_setter,
                    "is_auto_property": entity.is_auto_property,
                }
            )

        return doc_data

    async def find_by_name(self, name: str) -> List[CodeEntity]:
        """
        根据名称查找代码实体

        Args:
            name: 实体名称（支持模糊匹配）

        Returns:
            List[CodeEntity]: 匹配的代码实体列表
        """
        try:
            aql = "FOR c IN code_entities FILTER CONTAINS(c.name, @name) OR CONTAINS(c.full_name, @name) LIMIT 50 RETURN c"
            cursor = self.db.aql.execute(aql, bind_vars={"name": name})
            docs = []
            async for doc in cursor:
                docs.append(doc)
            return [self._to_entity(doc) for doc in docs]
        except Exception as e:
            logger.error(f"查找代码实体时出错: {e}", exc_info=True)
            raise


class KnowledgeGraphRepository:
    """知识图谱仓储"""

    def __init__(self):
        """
        初始化知识图谱仓储
        """
        self.knowledge_graph_manager = KnowledgeGraphManager()

    async def add_relationship(
        self,
        from_entity_key: str,
        to_entity_key: str,
        rel_type: str,
        properties: Optional[Dict] = None,
    ) -> None:
        """
        添加实体关系

        Args:
            from_entity_key: 起始实体文档键 (_key)
            to_entity_key: 目标实体文档键 (_key)
            rel_type: 关系类型
            properties: 关系属性
        """
        try:
            await self.knowledge_graph_manager.add_relationship(
                from_entity_key=from_entity_key,
                to_entity_key=to_entity_key,
                rel_type=rel_type,
                properties=properties,
            )
        except Exception as e:
            logger.error(f"添加实体关系时出错: {e}", exc_info=True)
            raise

    async def get_related_entities(
        self,
        entity_key: str,
        relationship_type: Optional[str] = None,
        direction: str = "BOTH",
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        获取相关实体

        Args:
            entity_key: 实体文档键 (_key)
            relationship_type: 关系类型，如果为None则返回所有关系
            direction: 关系方向，可选值：'INBOUND', 'OUTBOUND', 'ANY'
            limit: 返回结果数量限制

        Returns:
            List[Dict[str, Any]]: 相关实体列表
        """
        try:
            return await self.knowledge_graph_manager.get_related_entities(
                entity_key=entity_key,
                relationship_type=relationship_type,
                direction=direction,
                limit=limit,
            )
        except Exception as e:
            logger.error(f"获取相关实体时出错: {e}", exc_info=True)
            raise

    async def get_entity_impact(
        self, entity_key: str, max_depth: int = 3
    ) -> Dict[str, Any]:
        """
        获取实体影响分析

        Args:
            entity_key: 实体文档键 (_key)
            max_depth: 最大搜索深度

        Returns:
            Dict[str, Any]: 影响分析结果
        """
        try:
            return await self.knowledge_graph_manager.get_entity_impact(
                entity_key=entity_key, max_depth=max_depth
            )
        except Exception as e:
            logger.error(f"获取实体影响分析时出错: {e}", exc_info=True)
            raise
