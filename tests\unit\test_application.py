"""
应用程序核心单元测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from src.csharp_assistant.core.application import Application


class TestApplication:
    """应用程序测试类"""

    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        config = MagicMock()
        config.project_path = "/test/project"
        config.project_name = "TestProject"
        config.language_server_enabled = True
        return config

    @pytest.fixture
    def mock_components(self):
        """模拟应用组件"""
        components = {
            'knowledge_graph_manager': AsyncMock(),
            'omnisharp_client': AsyncMock(),
            'ai_manager': MagicMock(),
            'file_watcher': AsyncMock(),
            'mcp_service': AsyncMock()
        }
        return components

    @pytest.fixture
    def application(self, mock_config, mock_components):
        """创建应用程序实例"""
        with patch('src.csharp_assistant.core.application.get_config', return_value=mock_config):
            with patch('src.csharp_assistant.core.application.KnowledgeGraphRepository', return_value=mock_components['knowledge_graph_manager']):
                with patch('src.csharp_assistant.core.application.OmniSharpClient', return_value=mock_components['omnisharp_client']):
                    with patch('src.csharp_assistant.core.application.AIManager', return_value=mock_components['ai_manager']):
                        with patch('src.csharp_assistant.core.application.FileWatcher', return_value=mock_components['file_watcher']):
                            with patch('src.csharp_assistant.core.application.MCPService', return_value=mock_components['mcp_service']):
                                return Application()

    def test_init(self, application, mock_config, mock_components):
        """测试初始化"""
        assert application.config == mock_config
        assert application.knowledge_graph_manager == mock_components['knowledge_graph_manager']
        assert application.omnisharp_client == mock_components['omnisharp_client']
        assert application.ai_manager == mock_components['ai_manager']
        assert application.file_watcher == mock_components['file_watcher']
        assert application.mcp_service == mock_components['mcp_service']

    @pytest.mark.asyncio
    async def test_start_success(self, application, mock_components):
        """测试成功启动"""
        # 模拟文件分析结果
        mock_components['omnisharp_client'].analyze_code.return_value = {
            "classes": [{"name": "TestClass"}],
            "methods": [{"name": "TestMethod"}],
            "fields": [],
            "diagnostics": []
        }
        
        with patch('glob.glob', return_value=["/test/project/TestFile.cs"]):
            await application.start()
            
            # 验证各组件启动顺序
            mock_components['knowledge_graph_manager'].initialize_schema.assert_called_once()
            mock_components['omnisharp_client'].start.assert_called_once_with("/test/project")
            mock_components['omnisharp_client'].analyze_code.assert_called_once_with("/test/project/TestFile.cs")
            mock_components['knowledge_graph_manager'].update_from_analysis.assert_called_once()
            mock_components['mcp_service'].start.assert_called_once()
            mock_components['file_watcher'].start.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_language_server_disabled(self, application, mock_components):
        """测试语言服务器禁用时启动"""
        application.config.language_server_enabled = False
        
        await application.start()
        
        # 验证跳过代码分析
        mock_components['omnisharp_client'].analyze_code.assert_not_called()
        mock_components['knowledge_graph_manager'].update_from_analysis.assert_not_called()

    @pytest.mark.asyncio
    async def test_start_no_cs_files(self, application, mock_components):
        """测试没有C#文件时启动"""
        with patch('glob.glob', return_value=[]):
            await application.start()
            
            # 验证没有进行代码分析
            mock_components['omnisharp_client'].analyze_code.assert_not_called()
            mock_components['knowledge_graph_manager'].update_from_analysis.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_file_change_modified(self, application, mock_components):
        """测试处理文件修改事件"""
        file_path = "/test/project/TestFile.cs"
        
        # 模拟分析结果
        mock_components['omnisharp_client'].analyze_code.return_value = {
            "classes": [{"name": "TestClass"}],
            "methods": [],
            "fields": [],
            "diagnostics": []
        }
        
        await application._handle_file_change(file_path, "modified")
        
        # 验证文件被重新分析
        mock_components['omnisharp_client'].analyze_code.assert_called_once_with(file_path)
        mock_components['knowledge_graph_manager'].update_from_analysis.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_file_change_created(self, application, mock_components):
        """测试处理文件创建事件"""
        file_path = "/test/project/NewFile.cs"
        
        # 模拟分析结果
        mock_components['omnisharp_client'].analyze_code.return_value = {
            "classes": [],
            "methods": [{"name": "NewMethod"}],
            "fields": [],
            "diagnostics": []
        }
        
        await application._handle_file_change(file_path, "created")
        
        # 验证文件被分析
        mock_components['omnisharp_client'].analyze_code.assert_called_once_with(file_path)
        mock_components['knowledge_graph_manager'].update_from_analysis.assert_called_once()

    @pytest.mark.asyncio
    async def test_handle_file_change_deleted(self, application, mock_components):
        """测试处理文件删除事件"""
        file_path = "/test/project/DeletedFile.cs"
        
        await application._handle_file_change(file_path, "deleted")
        
        # 验证不进行代码分析
        mock_components['omnisharp_client'].analyze_code.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_file_update_language_server_disabled(self, application, mock_components):
        """测试语言服务器禁用时处理文件更新"""
        application.config.language_server_enabled = False
        file_path = "/test/project/TestFile.cs"
        
        await application._handle_file_update(file_path)
        
        # 验证跳过文件分析
        mock_components['omnisharp_client'].analyze_code.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_file_update_empty_analysis(self, application, mock_components):
        """测试处理空分析结果的文件更新"""
        file_path = "/test/project/TestFile.cs"
        
        # 模拟空分析结果
        mock_components['omnisharp_client'].analyze_code.return_value = None
        
        await application._handle_file_update(file_path)
        
        # 验证不更新知识图谱
        mock_components['knowledge_graph_manager'].update_from_analysis.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_file_update_exception(self, application, mock_components):
        """测试文件更新时异常处理"""
        file_path = "/test/project/TestFile.cs"
        
        # 模拟分析异常
        mock_components['omnisharp_client'].analyze_code.side_effect = Exception("分析失败")
        
        # 应该不抛出异常
        await application._handle_file_update(file_path)
        
        # 验证不更新知识图谱
        mock_components['knowledge_graph_manager'].update_from_analysis.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_file_deletion_exception(self, application):
        """测试文件删除时异常处理"""
        file_path = "/test/project/TestFile.cs"
        
        # 应该不抛出异常
        await application._handle_file_deletion(file_path)

    @pytest.mark.asyncio
    async def test_handle_file_change_exception(self, application):
        """测试文件变更处理异常"""
        file_path = "/test/project/TestFile.cs"
        
        with patch.object(application, '_handle_file_update', side_effect=Exception("处理失败")):
            # 应该不抛出异常
            await application._handle_file_change(file_path, "modified")

    @pytest.mark.asyncio
    async def test_stop_success(self, application, mock_components):
        """测试成功停止"""
        await application.stop()
        
        # 验证各组件停止顺序
        mock_components['file_watcher'].stop.assert_called_once()
        mock_components['omnisharp_client'].stop.assert_called_once()
        mock_components['mcp_service'].stop.assert_called_once()
        mock_components['knowledge_graph_manager'].close.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_with_analysis_exception(self, application, mock_components):
        """测试启动时分析异常"""
        # 模拟分析异常
        mock_components['omnisharp_client'].analyze_code.side_effect = Exception("分析失败")

        with patch('glob.glob', return_value=["/test/project/TestFile.cs"]):
            # 应该捕获异常并继续
            try:
                await application.start()
            except Exception:
                pass  # 预期会有异常，但应用应该继续启动其他组件

            # 验证其他组件仍然启动
            mock_components['mcp_service'].start.assert_called_once()
            mock_components['file_watcher'].start.assert_called_once()

    @pytest.mark.asyncio
    async def test_start_with_knowledge_graph_exception(self, application, mock_components):
        """测试启动时知识图谱异常"""
        # 模拟知识图谱异常
        mock_components['knowledge_graph_manager'].initialize_schema.side_effect = Exception("初始化失败")
        
        with pytest.raises(Exception):
            await application.start()

    @pytest.mark.asyncio
    async def test_file_watcher_callback_integration(self, application):
        """测试文件监控回调集成"""
        # 由于使用了mock，我们验证FileWatcher被正确初始化时传入了回调
        # 这个测试主要验证集成逻辑
        assert application.file_watcher is not None
        assert hasattr(application, '_handle_file_change')
