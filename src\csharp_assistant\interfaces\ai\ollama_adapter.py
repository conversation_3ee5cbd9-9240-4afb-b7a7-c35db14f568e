import logging
import json
from typing import Any, Dict, List, Optional
import httpx

from .base import AIService
from ...config.config import get_config

logger = logging.getLogger(__name__)


class OllamaAdapter(AIService):
    """Ollama AI 服务适配器"""

    def __init__(self):
        config = get_config()
        self.base_url = config.ollama_base_url
        self.model = config.ollama_model
        self.client = httpx.AsyncClient(base_url=self.base_url, timeout=60.0)
        logger.info(f"初始化Ollama适配器，模型: {self.model}, 地址: {self.base_url}")

    async def _generate_response(self, prompt: str, system_prompt: str = None) -> str:
        """生成AI响应"""
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 2048
                }
            }

            response = await self.client.post("/api/chat", json=payload)
            response.raise_for_status()

            result = response.json()
            if "message" in result and "content" in result["message"]:
                return result["message"]["content"]
            else:
                logger.error(f"Ollama响应格式异常: {result}")
                return "AI响应格式错误"

        except httpx.TimeoutException:
            logger.error("Ollama请求超时")
            return "AI服务请求超时，请稍后重试"
        except httpx.HTTPStatusError as e:
            logger.error(f"Ollama HTTP错误: {e.response.status_code} - {e.response.text}")
            return f"AI服务错误: {e.response.status_code}"
        except Exception as e:
            logger.error(f"Ollama请求失败: {e}", exc_info=True)
            return f"AI服务异常: {str(e)}"

    async def _check_model_availability(self) -> bool:
        """检查模型是否可用"""
        try:
            response = await self.client.get("/api/tags")
            response.raise_for_status()

            models = response.json().get("models", [])
            available_models = [model.get("name", "") for model in models]

            if self.model in available_models:
                return True
            else:
                logger.warning(f"模型 {self.model} 不可用，可用模型: {available_models}")
                return False

        except Exception as e:
            logger.error(f"检查模型可用性失败: {e}")
            return False

    async def analyze_code(self, code_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """分析代码结构"""
        logger.info("使用 Ollama 分析代码结构")

        # 检查模型可用性
        if not await self._check_model_availability():
            return {"analysis_result": "模型不可用，无法进行代码分析"}

        try:
            # 构建分析提示
            file_path = code_analysis.get('file_path', 'unknown')
            classes = code_analysis.get('classes', [])
            methods = code_analysis.get('methods', [])
            fields = code_analysis.get('fields', [])

            system_prompt = """你是一个专业的C#代码分析专家。请分析提供的代码结构，并给出详细的分析报告，包括：
1. 代码质量评估
2. 潜在问题识别
3. 改进建议
4. 设计模式识别
请用中文回答，保持专业和准确。"""

            analysis_prompt = f"""请分析以下C#代码结构：

文件路径: {file_path}

类信息:
{json.dumps(classes, indent=2, ensure_ascii=False)}

方法信息:
{json.dumps(methods, indent=2, ensure_ascii=False)}

字段信息:
{json.dumps(fields, indent=2, ensure_ascii=False)}

请提供详细的代码分析报告。"""

            analysis_result = await self._generate_response(analysis_prompt, system_prompt)

            return {
                "analysis_result": analysis_result,
                "file_path": file_path,
                "timestamp": "2024-01-01T00:00:00Z"  # 可以使用实际时间戳
            }

        except Exception as e:
            logger.error(f"代码分析失败: {e}", exc_info=True)
            return {"analysis_result": f"代码分析失败: {str(e)}"}

    async def get_suggestions(self, context: Dict[str, Any], query: Optional[str] = None) -> List[str]:
        """获取代码建议"""
        logger.info("使用 Ollama 获取代码建议")

        # 检查模型可用性
        if not await self._check_model_availability():
            return ["模型不可用，无法提供建议"]

        try:
            system_prompt = """你是一个专业的C#开发专家。基于提供的代码上下文和用户查询，提供实用的代码改进建议。
建议应该具体、可操作，并符合C#最佳实践。请用中文回答。"""

            # 构建上下文信息
            context_info = []
            if "project_name" in context:
                context_info.append(f"项目名称: {context['project_name']}")
            if "current_file" in context:
                context_info.append(f"当前文件: {context['current_file']}")
            if "classes" in context:
                context_info.append(f"相关类: {', '.join([cls.get('name', '') for cls in context['classes']])}")

            suggestion_prompt = f"""基于以下上下文信息，请提供代码改进建议：

上下文信息:
{chr(10).join(context_info)}

用户查询: {query or '请提供通用的代码改进建议'}

请提供3-5个具体的改进建议，每个建议应该包含原因和实施方法。"""

            suggestions_text = await self._generate_response(suggestion_prompt, system_prompt)

            # 解析建议文本为列表
            suggestions = []
            lines = suggestions_text.split('\n')
            current_suggestion = ""

            for line in lines:
                line = line.strip()
                if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.') or
                           line.startswith('4.') or line.startswith('5.') or line.startswith('-')):
                    if current_suggestion:
                        suggestions.append(current_suggestion.strip())
                    current_suggestion = line
                elif line and current_suggestion:
                    current_suggestion += " " + line

            if current_suggestion:
                suggestions.append(current_suggestion.strip())

            return suggestions if suggestions else [suggestions_text]

        except Exception as e:
            logger.error(f"获取建议失败: {e}", exc_info=True)
            return [f"获取建议失败: {str(e)}"]

    async def report_error(self, error_info: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """报告错误并获取修复建议"""
        logger.info("使用 Ollama 报告错误并获取修复建议")

        # 检查模型可用性
        if not await self._check_model_availability():
            return {"fix_suggestion": "模型不可用，无法提供错误修复建议"}

        try:
            system_prompt = """你是一个专业的C#错误诊断和修复专家。基于提供的错误信息和代码上下文，提供详细的错误分析和修复建议。
修复建议应该包含：
1. 错误原因分析
2. 具体的修复步骤
3. 预防类似错误的建议
请用中文回答，保持专业和准确。"""

            error_type = error_info.get('type', 'unknown')
            error_message = error_info.get('message', '')
            error_file = error_info.get('file', '')
            error_line = error_info.get('line', 0)
            error_column = error_info.get('column', 0)

            error_prompt = f"""请分析以下C#错误并提供修复建议：

错误类型: {error_type}
错误信息: {error_message}
文件位置: {error_file}:{error_line}:{error_column}

代码上下文:
{json.dumps(context, indent=2, ensure_ascii=False)}

请提供详细的错误分析和修复建议。"""

            fix_suggestion = await self._generate_response(error_prompt, system_prompt)

            return {
                "fix_suggestion": fix_suggestion,
                "error_type": error_type,
                "confidence": "medium",  # 可以根据错误类型调整
                "estimated_fix_time": "5-15分钟"
            }

        except Exception as e:
            logger.error(f"错误报告失败: {e}", exc_info=True)
            return {"fix_suggestion": f"错误分析失败: {str(e)}"}

    async def fix_error(self, error_info: Dict[str, Any], fix_suggestion: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """应用错误修复"""
        logger.info("使用 Ollama 应用错误修复")

        # 检查模型可用性
        if not await self._check_model_availability():
            return {"status": "failed", "message": "模型不可用，无法应用错误修复"}

        try:
            system_prompt = """你是一个专业的C#代码修复专家。基于错误信息和修复建议，生成具体的代码修复方案。
修复方案应该包含：
1. 需要修改的具体代码行
2. 修改前后的代码对比
3. 修复验证步骤
请用中文回答，确保修复方案的准确性。"""

            error_message = error_info.get('message', '')
            suggestion_text = fix_suggestion.get('fix_suggestion', '')

            fix_prompt = f"""基于以下错误信息和修复建议，生成具体的代码修复方案：

错误信息: {error_message}

修复建议: {suggestion_text}

代码上下文:
{json.dumps(context, indent=2, ensure_ascii=False)}

请提供具体的代码修复方案，包括修改前后的代码对比。"""

            fix_plan = await self._generate_response(fix_prompt, system_prompt)

            return {
                "status": "success",
                "fix_plan": fix_plan,
                "changes": {
                    "files_modified": [error_info.get('file', '')],
                    "lines_changed": [error_info.get('line', 0)]
                },
                "verification_steps": [
                    "编译项目检查语法错误",
                    "运行相关单元测试",
                    "验证功能是否正常"
                ]
            }

        except Exception as e:
            logger.error(f"错误修复失败: {e}", exc_info=True)
            return {"status": "failed", "message": f"错误修复失败: {str(e)}"}

    async def close(self):
        """关闭客户端连接"""
        if self.client:
            await self.client.aclose()
            logger.info("Ollama客户端连接已关闭")
