#!/usr/bin/env python3
"""
C# Assistant MCP Server

这是一个Model Context Protocol (MCP) 服务器，为C#项目提供智能代码分析和建议功能。
"""

import asyncio
import logging
import sys
import signal
from typing import Any, Dict, List, Optional
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.core.application import Application
from src.csharp_assistant.config.config import get_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)8s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class MCPServer:
    """MCP服务器主类"""
    
    def __init__(self):
        self.application = None
        self.running = False
        
    async def start(self):
        """启动MCP服务器"""
        try:
            logger.info("正在启动C# Assistant MCP服务器...")
            
            # 创建并启动应用程序
            self.application = Application()
            await self.application.start()
            
            self.running = True
            logger.info("C# Assistant MCP服务器已启动")
            
            # 保持服务器运行
            await self._keep_running()
            
        except Exception as e:
            logger.error(f"启动MCP服务器失败: {e}", exc_info=True)
            await self.stop()
            
    async def stop(self):
        """停止MCP服务器"""
        if not self.running:
            return
            
        logger.info("正在停止C# Assistant MCP服务器...")
        self.running = False
        
        if self.application:
            try:
                await self.application.stop()
            except Exception as e:
                logger.error(f"停止应用程序时出错: {e}", exc_info=True)
                
        logger.info("C# Assistant MCP服务器已停止")
        
    async def _keep_running(self):
        """保持服务器运行"""
        try:
            while self.running:
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            logger.info("收到取消信号，正在停止服务器...")
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在停止服务器...")
        finally:
            await self.stop()


def setup_signal_handlers(server: MCPServer):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，正在停止服务器...")
        asyncio.create_task(server.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    try:
        # 检查配置
        config = get_config()
        logger.info(f"项目路径: {config.project_path}")
        logger.info(f"项目名称: {config.project_name}")
        logger.info(f"语言服务器启用: {config.language_server_enabled}")
        logger.info(f"AI服务提供商: {config.ai_provider}")
        
        # 创建并启动服务器
        server = MCPServer()
        setup_signal_handlers(server)
        
        await server.start()
        
    except KeyboardInterrupt:
        logger.info("收到键盘中断，正在退出...")
    except Exception as e:
        logger.error(f"服务器运行时出错: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}", exc_info=True)
        sys.exit(1)
