"""
值对象

定义核心业务中的值对象。
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional

from .entities import CodeEntity  # 新增导入


class CodePosition:
    """代码位置"""

    def __init__(self, line: int, column: int, file_path: str):
        self.line = line
        self.column = column
        self.file_path = file_path

    def __str__(self) -> str:
        return f"{self.file_path}:{self.line}:{self.column}"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {"line": self.line, "column": self.column, "file_path": self.file_path}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CodePosition":
        """从字典创建"""
        return cls(
            line=data["line"], column=data["column"], file_path=data["file_path"]
        )


class CodeRange:
    """代码范围"""

    def __init__(self, start: CodePosition, end: CodePosition):
        self.start = start
        self.end = end

    def __str__(self) -> str:
        return f"{self.start}-{self.end}"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {"start": self.start.to_dict(), "end": self.end.to_dict()}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CodeRange":
        """从字典创建"""
        return cls(
            start=CodePosition.from_dict(data["start"]),
            end=CodePosition.from_dict(data["end"]),
        )


class CodeReference:
    """代码引用"""

    def __init__(self, name: str, full_name: str, position: CodePosition):
        self.name = name
        self.full_name = full_name
        self.position = position

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "full_name": self.full_name,
            "position": self.position.to_dict(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CodeReference":
        """从字典创建"""
        return cls(
            name=data["name"],
            full_name=data["full_name"],
            position=CodePosition.from_dict(data["position"]),
        )


class CodeChangeType(str, Enum):
    """代码变更类型"""

    ADDED = "added"
    MODIFIED = "modified"
    DELETED = "deleted"
    RENAMED = "renamed"


@dataclass
class CodeChange:
    """代码变更"""

    change_type: CodeChangeType
    entity_type: str
    old_name: Optional[str] = None
    new_name: Optional[str] = None
    file_path: Optional[str] = None
    position: Optional[CodePosition] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class CodeAnalysisResult:
    """代码分析结果"""

    def __init__(self):
        self.issues: List[Dict[str, Any]] = []
        self.metrics: Dict[str, Any] = {}
        self.dependencies: List[Dict[str, Any]] = []
        self.entities: List[CodeEntity] = []  # 新增 entities 列表

    def add_issue(self, issue: Dict[str, Any]) -> None:
        """添加问题"""
        self.issues.append(issue)

    def add_metric(self, name: str, value: Any) -> None:
        """添加指标"""
        self.metrics[name] = value

    def add_dependency(
        self, from_entity: str, to_entity: str, relation_type: str
    ) -> None:
        """添加依赖关系"""
        self.dependencies.append(
            {"from": from_entity, "to": to_entity, "type": relation_type}
        )

    def add_entity(self, entity: CodeEntity) -> None:  # 新增 add_entity 方法
        """添加代码实体"""
        self.entities.append(entity)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "issues": self.issues,
            "metrics": self.metrics,
            "dependencies": self.dependencies,
            "entities": [entity.to_dict() for entity in self.entities],  # 转换为字典
        }


# 导出所有值对象类
__all__ = [
    "CodePosition",
    "CodeRange",
    "CodeReference",
    "CodeChangeType",
    "CodeChange",
    "CodeAnalysisResult",
]
