"""
项目服务

提供项目相关的核心业务逻辑。
"""

import logging
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from ..domain.entities import CodeEntity, Project
from ..domain.value_objects import CodeAnalysisResult, CodeChange
from .knowledge_graph import KnowledgeGraphService
from ...interfaces.language_server.omnisharp_client import OmniSharpClient

logger = logging.getLogger(__name__)


class ProjectService:
    """项目服务"""

    def __init__(self, project: Project, knowledge_graph_service: KnowledgeGraphService, omnisharp_client: OmniSharpClient):
        """
        初始化项目服务

        Args:
            project: 项目实体
            knowledge_graph_service: 知识图谱服务实例
            omnisharp_client: OmniSharp 客户端实例
        """
        self.project = project
        self.knowledge_graph_service = knowledge_graph_service
        self.omnisharp_client = omnisharp_client
        self._code_analyzer = None

    @property
    def code_analyzer(self):
        """获取代码分析器实例"""
        if self._code_analyzer is None:
            from .code_analysis import CodeAnalyzer

            self._code_analyzer = CodeAnalyzer(self.project, self.omnisharp_client)
        return self._code_analyzer

    async def initialize_project(self) -> Dict[str, Any]:
        """
        初始化项目

        Returns:
            Dict[str, Any]: 初始化结果
        """
        try:
            # 1. 验证项目路径
            project_path = Path(self.project.path)
            if not project_path.exists():
                raise FileNotFoundError(f"项目路径不存在: {project_path}")

            # 2. 创建必要的项目结构
            self._ensure_project_structure()

            # 3. 分析项目代码
            analysis_result = await self.code_analyzer.analyze_project()

            # 4. 构建知识图谱
            await self._build_knowledge_graph(analysis_result)

            return {
                "success": True,
                "message": "项目初始化成功",
                "metrics": analysis_result.metrics,
                "issues": analysis_result.issues[:10],  # 只返回前10个问题
            }

        except Exception as e:
            logger.error(f"初始化项目时出错: {e}", exc_info=True)
            return {"success": False, "message": f"初始化项目失败: {str(e)}"}

    def _ensure_project_structure(self) -> None:
        """确保项目结构完整"""
        project_path = Path(self.project.path)

        # 创建必要的目录
        dirs_to_create = [
            project_path / "src",
            project_path / "tests",
            project_path / "docs",
            project_path / "config",
        ]

        for dir_path in dirs_to_create:
            dir_path.mkdir(parents=True, exist_ok=True)

    async def _build_knowledge_graph(self, analysis_result: CodeAnalysisResult) -> None:
        """
        构建知识图谱

        Args:
            analysis_result: 代码分析结果
        """
        # 初始化数据库（如果需要）
        await self.knowledge_graph_service.initialize_database()

        # 从分析结果更新知识图谱
        await self.knowledge_graph_service.update_from_analysis(analysis_result)

        logger.info(f"知识图谱构建完成，共处理 {len(analysis_result.dependencies)} 个依赖关系")

    async def get_project_structure(self) -> Dict[str, Any]:
        """
        获取项目结构

        Returns:
            Dict[str, Any]: 项目结构信息
        """
        project_path = Path(self.project.path)

        def scan_directory(directory: Path) -> List[Dict[str, Any]]:
            """递归扫描目录结构"""
            result = []

            for item in directory.iterdir():
                if item.is_dir() and item.name not in [
                    "__pycache__",
                    ".git",
                    ".vs",
                    "bin",
                    "obj",
                ]:
                    result.append(
                        {
                            "name": item.name,
                            "type": "directory",
                            "path": str(item.relative_to(project_path)),
                            "children": scan_directory(item),
                        }
                    )
                elif item.is_file() and item.suffix in [".cs", ".csproj", ".sln"]:
                    result.append(
                        {
                            "name": item.name,
                            "type": "file",
                            "path": str(item.relative_to(project_path)),
                            "size": item.stat().st_size,
                        }
                    )

            return result

        return {
            "name": self.project.name,
            "path": str(project_path),
            "children": scan_directory(project_path),
        }

    async def search_code(
        self, query: str, max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """
        搜索代码

        Args:
            query: 搜索查询
            max_results: 最大结果数

        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        results = await self.knowledge_graph_service.query_context(query, limit=max_results)

        formatted_results = []
        for doc in results:
            formatted_results.append({
                "name": doc.get("name"),
                "full_name": doc.get("full_name"),
                "type": doc.get("type"),
                "documentation": doc.get("documentation", ""),
                # Add other relevant fields from the ArangoDB document
            })
        return formatted_results

    async def refactor_code(self, refactoring: Dict[str, Any]) -> Dict[str, Any]:
        """
        重构代码

        Args:
            refactoring: 重构操作定义

        Returns:
            Dict[str, Any]: 重构结果
        """
        try:
            refactoring_type = refactoring.get("type")

            if refactoring_type == "rename":
                return await self._refactor_rename(
                    old_name=refactoring["old_name"], new_name=refactoring["new_name"]
                )
            elif refactoring_type == "extract_method":
                return await self._refactor_extract_method(
                    file_path=refactoring["file_path"],
                    start_line=refactoring["start_line"],
                    end_line=refactoring["end_line"],
                    new_method_name=refactoring["new_method_name"],
                )
            else:
                raise ValueError(f"不支持的重构类型: {refactoring_type}")

        except Exception as e:
            logger.error(f"重构代码时出错: {e}", exc_info=True)
            return {"success": False, "message": f"重构代码失败: {str(e)}"}

    async def _refactor_rename(self, old_name: str, new_name: str) -> Dict[str, Any]:
        """
        重命名代码元素

        Args:
            old_name: 旧名称
            new_name: 新名称

        Returns:
            Dict[str, Any]: 重命名结果
        """
        # 这里简化处理，实际应该更新代码文件
        logger.info(f"将 {old_name} 重命名为 {new_name}")

        return {
            "success": True,
            "message": f"成功将 {old_name} 重命名为 {new_name}",
            "changes": [
                {
                    "file": "path/to/file.cs",
                    "line": 10,
                    "old_text": f"class {old_name}",
                    "new_text": f"class {new_name}",
                }
            ],
        }

    async def _refactor_extract_method(
        self, file_path: str, start_line: int, end_line: int, new_method_name: str
    ) -> Dict[str, Any]:
        """
        提取方法

        Args:
            file_path: 文件路径
            start_line: 开始行号
            end_line: 结束行号
            new_method_name: 新方法名称

        Returns:
            Dict[str, Any]: 提取方法结果
        """
        # 这里简化处理，实际应该解析代码并生成新的方法
        logger.info(
            f"从 {file_path} 的第 {start_line} 行到第 {end_line} 行提取方法 {new_method_name}"
        )

        return {
            "success": True,
            "message": f"成功提取方法 {new_method_name}",
            "changes": [
                {
                    "file": file_path,
                    "line": start_line,
                    "old_text": "// 原始代码...",
                    "new_text": "// 提取后的方法调用...",
                },
                {
                    "file": file_path,
                    "line": end_line + 1,
                    "old_text": "",
                    "new_text": "// 新提取的方法...",
                },
            ],
        }
