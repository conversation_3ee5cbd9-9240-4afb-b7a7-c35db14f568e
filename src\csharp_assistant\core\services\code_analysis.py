"""
代码分析服务

提供代码分析功能，包括语法检查、代码质量分析等。
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from ..domain.entities import (
    ClassDefinition,
    CodeEntity,
    EntityType,
    MethodDefinition,
    Project,
    PropertyDefinition,
)
from ..domain.value_objects import (
    CodeAnalysisResult,
    CodeChange,
    CodeChangeType,
    CodePosition,
    CodeRange,
)
from ...interfaces.language_server.omnisharp_client import OmniSharpClient

logger = logging.getLogger(__name__)


class CodeAnalyzer:
    """代码分析器"""

    def __init__(self, project: Project, omnisharp_client: OmniSharpClient):
        """
        初始化代码分析器

        Args:
            project: 项目实体
            omnisharp_client: OmniSharp 客户端实例
        """
        self.project = project
        self.omnisharp_client = omnisharp_client
        self._code_entities: Dict[str, CodeEntity] = {}

    async def analyze_project(self) -> CodeAnalysisResult:
        """
        分析整个项目

        Returns:
            CodeAnalysisResult: 分析结果
        """
        result = CodeAnalysisResult()

        try:
            # 1. 收集项目中的所有代码文件
            code_files = self._find_code_files()

            # 2. 分析每个代码文件
            for file_path in code_files:
                await self._analyze_file(file_path, result)

            # 3. 分析代码依赖关系
            # self._analyze_dependencies(result)

            # 4. 计算代码指标
            # self._calculate_metrics(result)

        except Exception as e:
            logger.error(f"分析项目时出错: {e}", exc_info=True)
            raise

        return result

    def _find_code_files(self) -> List[Path]:
        """
        查找项目中的所有代码文件

        Returns:
            List[Path]: 代码文件路径列表
        """
        project_path = Path(self.project.path)
        code_files = []

        # 查找所有 .cs 文件
        for ext in ["*.cs", "*.csproj", "*.sln"]:
            code_files.extend(project_path.rglob(ext))

        return code_files

    async def _analyze_file(self, file_path: Path, result: CodeAnalysisResult) -> None:
        """
        分析单个代码文件

        Args:
            file_path: 文件路径
            result: 分析结果对象
        """
        try:
            # 这里简化处理，实际应该解析 C# 代码
            # 可以使用 Roslyn 或其他 C# 解析器进行更深入的分析

            # 示例：记录文件中的类和方法
            if file_path.suffix == ".cs":
                await self._parse_csharp_file(file_path, result)

        except Exception as e:
            logger.error(f"分析文件 {file_path} 时出错: {e}", exc_info=True)
            result.add_issue(
                {
                    "type": "error",
                    "message": f"分析文件时出错: {str(e)}",
                    "file": str(file_path),
                    "severity": "error",
                }
            )

    async def _parse_csharp_file(
        self, file_path: Path, result: CodeAnalysisResult
    ) -> None:
        """
        解析 C# 代码文件

        Args:
            file_path: 文件路径
            result: 分析结果对象
        """
        analysis_data = await self.omnisharp_client.analyze_code(str(file_path))

        # Process classes
        for class_data in analysis_data.get("classes", []):
            class_def = ClassDefinition(
                name=class_data.get("name"),
                full_name=class_data.get("full_name"),
                type=EntityType.CLASS,
                access_modifier=class_data.get("access_modifier", "public"),
                namespace=class_data.get("namespace"),
                is_static=class_data.get("is_static", False),
                is_abstract=class_data.get("is_abstract", False),
                is_sealed=class_data.get("is_sealed", False),
                base_types=class_data.get("base_types", []),
            )
            self._code_entities[class_def.full_name] = class_def
            result.add_entity(class_def)

            # Process methods
            for method_data in class_data.get("methods", []):
                method_def = MethodDefinition(
                    name=method_data.get("name"),
                    full_name=method_data.get("full_name"),
                    type=EntityType.METHOD,
                    access_modifier=method_data.get("access_modifier", "public"),
                    return_type=method_data.get("return_type"),
                    is_async=method_data.get("is_async", False),
                    is_extension=method_data.get("is_extension", False),
                    is_override=method_data.get("is_override", False),
                    is_virtual=method_data.get("is_virtual", False),
                    is_abstract=method_data.get("is_abstract", False),
                    parameters=method_data.get("parameters", []),
                )
                self._code_entities[method_def.full_name] = method_def
                result.add_entity(method_def)

            # Process fields/properties
            for field_data in class_data.get("fields", []):
                prop_def = PropertyDefinition(
                    name=field_data.get("name"),
                    full_name=field_data.get("full_name"),
                    type=EntityType.PROPERTY,
                    access_modifier=field_data.get("access_modifier", "public"),
                    property_type=field_data.get("type"),
                    has_getter=field_data.get("has_getter", False),
                    has_setter=field_data.get("has_setter", False),
                    is_auto_property=field_data.get("is_auto_property", False),
                )
                self._code_entities[prop_def.full_name] = prop_def
                result.add_entity(prop_def)

        # Process diagnostics (issues)
        for diagnostic in analysis_data.get("diagnostics", []):
            result.add_issue({
                "type": diagnostic.get("type", "error"),
                "message": diagnostic.get("message"),
                "file": diagnostic.get("file"),
                "line": diagnostic.get("line"),
                "column": diagnostic.get("column"),
                "severity": diagnostic.get("severity", "error"),
            })
