[pytest]
# 测试文件匹配模式
testpaths = tests
python_files = test_*.py
asyncio_mode = auto

# 测试发现规则
python_classes = Test* *Test *TestCase
python_functions = test_*

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试标记
markers =
    unit: 标记为单元测试
    integration: 标记为集成测试
    slow: 标记为慢速测试

# 测试运行选项
addopts = -v --tb=short --strict-markers

# 测试报告
junit_suite_name = csharp-assistant-tests
junit_logging = all
junit_duration_report = call
