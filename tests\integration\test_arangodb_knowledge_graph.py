import pytest
from src.csharp_assistant.infrastructure.persistence.arangodb_repository import KnowledgeGraphManager
from src.csharp_assistant.config.config import get_config
import asyncio

# Use a fixture to initialize and clean up ArangoDB for tests
@pytest.fixture(scope="function")
async def arangodb_manager():
    config = get_config()
    # Ensure a test database is used
    test_db_name = "test_csharp_knowledge_graph"
    
    # Temporarily modify config for test database
    original_db_name = config.arangodb_database
    config.arangodb_database = test_db_name

    client = KnowledgeGraphManager().client
    sys_db = client.db(
        "_system",
        username=config.arangodb_user,
        password=config.arangodb_password,
    )

    if sys_db.has_database(test_db_name):
        sys_db.delete_database(test_db_name)
    
    sys_db.create_database(test_db_name)

    manager = KnowledgeGraphManager()
    await manager.initialize_schema()
    yield manager
    
    # Clean up
    sys_db.delete_database(test_db_name)
    config.arangodb_database = original_db_name # Restore original db name

@pytest.mark.asyncio
async def test_initialize_schema(arangodb_manager):
    # Schema initialization is part of the fixture setup
    # Just check if collections exist
    db = arangodb_manager.db
    assert db.has_collection("Project")
    assert db.has_collection("File")
    assert db.has_collection("Class")
    assert db.has_collection("Method")
    assert db.has_collection("Parameter")
    assert db.has_collection("Field")
    assert db.has_graph("csharp_knowledge_graph")

@pytest.mark.asyncio
async def test_update_from_analysis(arangodb_manager):
    project_name = "TestProject"
    analysis_data = {
        "files": [
            {
                "path": "src/Program.cs",
                "classes": [
                    {
                        "name": "Program",
                        "namespace": "TestProject",
                        "methods": [
                            {
                                "name": "Main",
                                "return_type": "void",
                                "parameters": [
                                    {"name": "args", "type": "string[]"}
                                ],
                                "calls": ["System.Console.WriteLine"]
                            }
                        ],
                        "fields": []
                    }
                ]
            }
        ]
    }

    await arangodb_manager.update_from_analysis(project_name, analysis_data)

    db = arangodb_manager.db

    # Verify Project
    project_cursor = db.aql.execute(f"FOR p IN Project FILTER p.name == '{project_name}' RETURN p")
    projects = [doc for doc in project_cursor]
    assert len(projects) == 1
    assert projects[0]["name"] == project_name

    # Verify File
    file_cursor = db.aql.execute(f"FOR f IN File FILTER f.path == 'src/Program.cs' RETURN f")
    files = [doc for doc in file_cursor]
    assert len(files) == 1
    assert files[0]["path"] == "src/Program.cs"

    # Verify Class
    class_cursor = db.aql.execute(f"FOR c IN Class FILTER c.name == 'Program' RETURN c")
    classes = [doc for doc in class_cursor]
    assert len(classes) == 1
    assert classes[0]["name"] == "Program"
    assert classes[0]["namespace"] == "TestProject"

    # Verify Method
    method_cursor = db.aql.execute(f"FOR m IN Method FILTER m.name == 'Main' RETURN m")
    methods = [doc for doc in method_cursor]
    assert len(methods) == 1
    assert methods[0]["name"] == "Main"
    assert methods[0]["return_type"] == "void"
    assert methods[0]["full_name"] == "TestProject.Program.Main"

    # Verify Parameter
    param_cursor = db.aql.execute(f"FOR p IN Parameter FILTER p.name == 'args' RETURN p")
    params = [doc for doc in param_cursor]
    assert len(params) == 1
    assert params[0]["name"] == "args"
    assert params[0]["type"] == "string[]"

    # Verify relationships
    # Project CONTAINS_FILE File
    edge_cursor = db.aql.execute(f"FOR v, e IN 1..1 OUTBOUND '{projects[0]['_id']}' CONTAINS_FILE RETURN {{v: v.path, e: e._key}}")
    edges = [doc for doc in edge_cursor]
    assert any(edge['v'] == 'src/Program.cs' for edge in edges)

    # File CONTAINS_CLASS Class
    edge_cursor = db.aql.execute(f"FOR v, e IN 1..1 OUTBOUND '{files[0]['_id']}' CONTAINS_CLASS RETURN {{v: v.name, e: e._key}}")
    edges = [doc for doc in edge_cursor]
    assert any(edge['v'] == 'Program' for edge in edges)

    # Class CONTAINS_METHOD Method
    edge_cursor = db.aql.execute(f"FOR v, e IN 1..1 OUTBOUND '{classes[0]['_id']}' CONTAINS_METHOD RETURN {{v: v.name, e: e._key}}")
    edges = [doc for doc in edge_cursor]
    assert any(edge['v'] == 'Main' for edge in edges)

    # Method HAS_PARAMETER Parameter
    edge_cursor = db.aql.execute(f"FOR v, e IN 1..1 OUTBOUND '{methods[0]['_id']}' HAS_PARAMETER RETURN {{v: v.name, e: e._key}}")
    edges = [doc for doc in edge_cursor]
    assert any(edge['v'] == 'args' for edge in edges)

    # Method CALLS Method (System.Console.WriteLine)
    # This requires System.Console.WriteLine to be inserted as a method first
    # For simplicity, we'll just check the edge exists
    callee_full_name = "System.Console.WriteLine"
    callee_key = callee_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
    callee_doc = db.collection("Method").insert({"_key": callee_key, "full_name": callee_full_name}, overwrite=True)

    edge_cursor = db.aql.execute(f"FOR v, e IN 1..1 OUTBOUND '{methods[0]['_id']}' CALLS RETURN {{v: v.full_name, e: e._key}}")
    edges = [doc for doc in edge_cursor]
    assert any(edge['v'] == callee_full_name for edge in edges)


@pytest.mark.asyncio
async def test_query_context(arangodb_manager):
    # Ensure data exists for querying
    project_name = "QueryTestProject"
    analysis_data = {
        "files": [
            {
                "path": "src/QueryClass.cs",
                "classes": [
                    {
                        "name": "QueryClass",
                        "namespace": "QueryNamespace",
                        "methods": [
                            {"name": "SearchMethod", "return_type": "int"},
                            {"name": "AnotherMethod", "return_type": "bool"}
                        ],
                        "fields": [
                            {"name": "QueryField", "type": "string"}
                        ]
                    }
                ]
            }
        ]
    }
    await arangodb_manager.update_from_analysis(project_name, analysis_data)

    # Test query for class name
    results = await arangodb_manager.query_context("QueryClass")
    assert len(results) > 0
    assert any(doc["name"] == "QueryClass" for doc in results)

    # Test query for method name
    results = await arangodb_manager.query_context("SearchMethod")
    assert len(results) > 0
    assert any(doc["name"] == "SearchMethod" for doc in results)

    # Test query for full method name
    results = await arangodb_manager.query_context("QueryNamespace.QueryClass.SearchMethod")
    assert len(results) > 0
    assert any(doc["full_name"] == "QueryNamespace.QueryClass.SearchMethod" for doc in results)

    # Test query for field name (should not return as it's not in Class or Method collections)
    results = await arangodb_manager.query_context("QueryField")
    assert len(results) == 0
