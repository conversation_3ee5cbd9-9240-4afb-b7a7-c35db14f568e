import logging
import json
from typing import Any, Dict, List, Optional
from openai import OpenAI

from .base import AIService
from ...config.config import get_config

logger = logging.getLogger(__name__)


class OpenAIAdapter(AIService):
    """OpenAI AI 服务适配器"""

    def __init__(self):
        config = get_config()
        if not config.openai_api_key:
            logger.warning("OpenAI API密钥未配置")
            self.client = None
        else:
            self.client = OpenAI(api_key=config.openai_api_key)
        self.model = config.openai_model

    def _is_available(self) -> bool:
        """检查OpenAI服务是否可用"""
        return self.client is not None

    async def analyze_code(self, code_analysis: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("使用 OpenAI 分析代码结构")

        if not self._is_available():
            return {"analysis_result": "OpenAI API密钥未配置，无法进行代码分析"}

        try:
            file_path = code_analysis.get('file_path', 'unknown')

            system_prompt = """你是一个专业的C#代码分析专家。请分析提供的代码结构，并给出详细的分析报告，包括：
1. 代码质量评估
2. 潜在问题识别
3. 改进建议
4. 设计模式识别
请用中文回答，保持专业和准确。"""

            user_prompt = f"""请分析以下C#代码结构：

文件路径: {file_path}
代码结构: {json.dumps(code_analysis, indent=2, ensure_ascii=False)}

请提供详细的代码分析报告。"""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=2048
            )

            analysis_result = response.choices[0].message.content

            return {
                "analysis_result": analysis_result,
                "file_path": file_path,
                "model_used": self.model
            }

        except Exception as e:
            logger.error(f"OpenAI代码分析失败: {e}", exc_info=True)
            return {"analysis_result": f"OpenAI代码分析失败: {str(e)}"}

    async def get_suggestions(self, context: Dict[str, Any], query: Optional[str] = None) -> List[str]:
        logger.info("使用 OpenAI 获取代码建议")

        if not self._is_available():
            return ["OpenAI API密钥未配置，无法提供建议"]

        try:
            system_prompt = """你是一个专业的C#开发专家。基于提供的代码上下文和用户查询，提供实用的代码改进建议。
建议应该具体、可操作，并符合C#最佳实践。请用中文回答。"""

            user_prompt = f"""基于以下上下文信息，请提供代码改进建议：

上下文: {json.dumps(context, indent=2, ensure_ascii=False)}
用户查询: {query or '请提供通用的代码改进建议'}

请提供3-5个具体的改进建议。"""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=1024
            )

            suggestions_text = response.choices[0].message.content

            # 简单解析建议
            suggestions = [s.strip() for s in suggestions_text.split('\n') if s.strip() and (s.strip().startswith(('1.', '2.', '3.', '4.', '5.', '-')))]

            return suggestions if suggestions else [suggestions_text]

        except Exception as e:
            logger.error(f"OpenAI获取建议失败: {e}", exc_info=True)
            return [f"OpenAI获取建议失败: {str(e)}"]

    async def report_error(self, error_info: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("使用 OpenAI 报告错误并获取修复建议")

        if not self._is_available():
            return {"fix_suggestion": "OpenAI API密钥未配置，无法提供错误修复建议"}

        try:
            system_prompt = """你是一个专业的C#错误诊断和修复专家。基于提供的错误信息和代码上下文，提供详细的错误分析和修复建议。
请用中文回答，保持专业和准确。"""

            user_prompt = f"""请分析以下C#错误并提供修复建议：

错误信息: {json.dumps(error_info, indent=2, ensure_ascii=False)}
代码上下文: {json.dumps(context, indent=2, ensure_ascii=False)}

请提供详细的错误分析和修复建议。"""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,  # 错误修复需要更准确
                max_tokens=1024
            )

            fix_suggestion = response.choices[0].message.content

            return {
                "fix_suggestion": fix_suggestion,
                "confidence": "high",
                "model_used": self.model
            }

        except Exception as e:
            logger.error(f"OpenAI错误报告失败: {e}", exc_info=True)
            return {"fix_suggestion": f"OpenAI错误分析失败: {str(e)}"}

    async def fix_error(self, error_info: Dict[str, Any], fix_suggestion: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("使用 OpenAI 应用错误修复")

        if not self._is_available():
            return {"status": "failed", "message": "OpenAI API密钥未配置，无法应用错误修复"}

        try:
            system_prompt = """你是一个专业的C#代码修复专家。基于错误信息和修复建议，生成具体的代码修复方案。
请用中文回答，确保修复方案的准确性。"""

            user_prompt = f"""基于以下信息生成具体的代码修复方案：

错误信息: {json.dumps(error_info, indent=2, ensure_ascii=False)}
修复建议: {json.dumps(fix_suggestion, indent=2, ensure_ascii=False)}
代码上下文: {json.dumps(context, indent=2, ensure_ascii=False)}

请提供具体的代码修复方案。"""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,
                max_tokens=1024
            )

            fix_plan = response.choices[0].message.content

            return {
                "status": "success",
                "fix_plan": fix_plan,
                "model_used": self.model
            }

        except Exception as e:
            logger.error(f"OpenAI错误修复失败: {e}", exc_info=True)
            return {"status": "failed", "message": f"OpenAI错误修复失败: {str(e)}"}
