"""
应用核心
"""

import asyncio
import logging
import glob
import os

from ..infrastructure.adapters.file_watcher import FileWatcher
from ..infrastructure.persistence import KnowledgeGraphRepository
from ..interfaces.language_server.omnisharp_client import OmniSharpClient
from ..interfaces.ai.ai_manager import AIManager
from ..infrastructure.adapters.mcp_service import MCPService
from ..config.config import get_config

logger = logging.getLogger(__name__)


class Application:
    """应用核心类"""

    def __init__(self):
        self.config = get_config()
        self.file_watcher = FileWatcher()
        self.knowledge_graph_manager = KnowledgeGraphRepository()
        self.omnisharp_client = OmniSharpClient()
        self.ai_manager = AIManager()
        self.mcp_service = MCPService(
            knowledge_graph_manager=self.knowledge_graph_manager,
            omnisharp_client=self.omnisharp_client,
            ai_manager=self.ai_manager,
        )

    async def start(self):
        """启动应用"""
        logger.info("应用已启动")
        await self.knowledge_graph_manager.initialize_schema()
        await self.omnisharp_client.start(self.config.project_path)

        # Perform initial code analysis and populate knowledge graph
        if self.config.language_server_enabled:
            logger.info("开始进行初始代码分析并填充知识图谱")
            csharp_files = glob.glob(os.path.join(self.config.project_path, "**", "*.cs"), recursive=True)
            project_analysis_results = {"files": []}
            for file_path in csharp_files:
                logger.info(f"分析文件: {file_path}")
                analysis_result = await self.omnisharp_client.analyze_code(file_path)
                project_analysis_results["files"].append({"path": file_path, **analysis_result})
            
            if project_analysis_results["files"]:
                await self.knowledge_graph_manager.update_from_analysis(
                    self.config.project_name,
                    project_analysis_results
                )
                logger.info("知识图谱填充完成")
            else:
                logger.warning("未找到 C# 文件进行分析或分析结果为空")

        await self.mcp_service.start()
        await self.file_watcher.start()

    async def stop(self):
        """停止应用"""
        await self.file_watcher.stop()
        await self.omnisharp_client.stop()
        await self.mcp_service.stop()
        self.knowledge_graph_manager.close()
        logger.info("应用已停止")
