"""
测试配置

包含测试所需的固件和配置。
"""

import os
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

print(f"sys.path: {sys.path}")

from arango import ArangoClient
from arango.collection import StandardCollection
from arango.database import StandardDatabase

from csharp_assistant.config import Config, get_config
from csharp_assistant.infrastructure.persistence import UnitOfWork, UnitOfWorkFactory

# 测试配置
TEST_CONFIG = {
    "PROJECT_NAME": "TestProject",
    "PROJECT_PATH": str(Path(__file__).parent / "test_project"),
    "ARANGODB_HOST": "localhost",
    "ARANGODB_PORT": 8529,
    "ARANGODB_USER": "root",
    "ARANGODB_PASSWORD": "root_password",
    "ARANGODB_DATABASE": "_system",
    "LANGUAGE_SERVER_PATH": "C:\\path\\to\\omnisharp\\OmniSharp.exe",
    "LOG_LEVEL": "DEBUG",
}


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境"""
    # 创建测试项目目录
    test_project_path = Path(TEST_CONFIG["PROJECT_PATH"])
    test_project_path.mkdir(parents=True, exist_ok=True)

    # 创建测试C#项目文件
    (test_project_path / "TestProject.csproj").write_text(
        """<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>
</Project>"""
    )

    # 创建测试C#代码文件
    (test_project_path / "Program.cs").write_text(
        """using System;

namespace TestProject
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Hello, World!");
        }
        
        public int Add(int a, int b)
        {
            return a + b;
        }
    }
}"""
    )

    yield  # 测试运行

    # 测试完成后清理
    # 注意：在实际项目中，你可能想要保留测试生成的文件以便调试
    # import shutil
    # shutil.rmtree(test_project_path)


@pytest.fixture
def mock_config():
    """模拟配置"""
    with patch("src.csharp_assistant.config.Config") as mock:
        mock.return_value = MagicMock(
            **{
                "project_name": TEST_CONFIG["PROJECT_NAME"],
                "project_path": TEST_CONFIG["PROJECT_PATH"],
                "arangodb_host": TEST_CONFIG["ARANGODB_HOST"],
                "arangodb_port": TEST_CONFIG["ARANGODB_PORT"],
                "arangodb_user": TEST_CONFIG["ARANGODB_USER"],
                "arangodb_password": TEST_CONFIG["ARANGODB_PASSWORD"],
                "arangodb_database": TEST_CONFIG["ARANGODB_DATABASE"],
                "language_server_path": TEST_CONFIG["LANGUAGE_SERVER_PATH"],
                "log_level": TEST_CONFIG["LOG_LEVEL"],
            }
        )
        yield mock


@pytest.fixture
def mock_db():
    """模拟 ArangoDB 数据库连接"""
    mock_db_instance = MagicMock(spec=StandardDatabase)
    mock_db_instance.collection.return_value = MagicMock(spec=StandardCollection)
    mock_db_instance.aql.execute.return_value = AsyncMock()
    mock_db_instance.has_graph.return_value = False
    mock_db_instance.graph.return_value = MagicMock()
    return mock_db_instance


@pytest.fixture
def unit_of_work(mock_db):
    """工作单元实例"""
    return UnitOfWork(mock_db)


@pytest.fixture
def unit_of_work_factory(mock_db):
    """工作单元工厂实例"""
    return UnitOfWorkFactory(mock_db)


@pytest.fixture
def test_project():
    """测试项目实体"""
    from csharp_assistant.core.domain.entities import Project

    return Project(name=TEST_CONFIG["PROJECT_NAME"], path=TEST_CONFIG["PROJECT_PATH"])
