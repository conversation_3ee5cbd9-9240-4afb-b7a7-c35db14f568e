"""
应用设置
"""

import os
from dotenv import load_dotenv

load_dotenv()


class Settings:
    """应用设置类"""

    # 项目配置
    PROJECT_NAME: str = os.getenv("PROJECT_NAME", "MyCSharpProject")
    PROJECT_PATH: str = os.getenv("PROJECT_PATH")
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # 语言服务器配置
    LANGUAGE_SERVER_PATH: str = os.getenv("LANGUAGE_SERVER_PATH")

    # ArangoDB 配置
    ARANGODB_HOST: str = os.getenv("ARANGODB_HOST", "localhost")
    ARANGODB_PORT: int = int(os.getenv("ARANGODB_PORT", "8529"))
    ARANGODB_USER: str = os.getenv("ARANGODB_USER", "root")
    ARANGODB_PASSWORD: str = os.getenv("ARANGODB_PASSWORD", "your-root-password")
    ARANGODB_DATABASE: str = os.getenv("ARANGODB_DATABASE", "_system")

    

    # AI 配置
    AI_DEFAULT_PROVIDER: str = os.getenv("AI_DEFAULT_PROVIDER", "openai")

    # OpenAI 配置
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4")

    # Google Gemini 配置
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY")
    GEMINI_MODEL: str = os.getenv("GEMINI_MODEL", "gemini-pro")

    # Ollama 配置
    OLLAMA_BASE_URL: str = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    OLLAMA_MODEL: str = os.getenv("OLLAMA_MODEL", "llama3")

    # MCP 服务配置
    MCP_HOST: str = os.getenv("MCP_HOST", "0.0.0.0")
    MCP_PORT: int = int(os.getenv("MCP_PORT", "8000"))

    def validate(self) -> bool:
        """验证配置是否有效"""
        required_vars = {
            "PROJECT_PATH": self.PROJECT_PATH,
            "LANGUAGE_SERVER_PATH": self.LANGUAGE_SERVER_PATH,
            "ARANGODB_HOST": self.ARANGODB_HOST,
            "ARANGODB_PORT": self.ARANGODB_PORT,
            "ARANGODB_USER": self.ARANGODB_USER,
            "ARANGODB_PASSWORD": self.ARANGODB_PASSWORD,
            "ARANGODB_DATABASE": self.ARANGODB_DATABASE,
        }

        missing = [var for var, value in required_vars.items() if not value]
        if missing:
            raise ValueError(f"Missing required environment variables: {', '.join(missing)}")

        return True


settings = Settings()
