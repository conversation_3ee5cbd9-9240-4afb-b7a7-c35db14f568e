from setuptools import find_packages, setup

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="csharp-assistant",
    version="0.1.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="An intelligent development tool for C# projects",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/csharp-assistant",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.8",
    install_requires=[
        "python-arango>=7.0.0",
        "pydantic>=1.10.0",
        "python-dotenv>=0.21.0",
        "aiohttp>=3.8.0",
        "typing-extensions>=4.0.0",
        "typer>=0.9.0",
        "rich>=13.0.0",
        "watchdog>=3.0.0",
        "httpx>=0.24.0",
        "openai>=0.27.0",
        "google-generativeai>=0.3.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.20.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "sphinx>=7.0.0",
            "sphinx-rtd-theme>=1.2.0",
            "pre-commit>=3.0.0",
        ],
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Topic :: Software Development",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Software Development :: Quality Assurance",
    ],
    entry_points={
        "console_scripts": [
            "csharp-assistant=csharp_assistant.interfaces.cli.main:main",
            "csharp-assistant-mcp=csharp_assistant.mcp_server:main",
        ],
    },
)
