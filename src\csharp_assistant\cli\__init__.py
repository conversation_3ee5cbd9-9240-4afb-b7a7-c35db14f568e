"""
C# 助手命令行界面

提供与 C# 助手交互的命令行工具。
"""

import asyncio
import logging
from pathlib import Path
from typing import Optional

import typer

from ..config import get_config
from ..core.domain.entities import Project
from ..core.services.project_service import ProjectService
from ..core.services.knowledge_graph import KnowledgeGraphService
from ..interfaces.language_server.omnisharp_client import OmniSharpClient

# 创建 Typer 应用
app = typer.Typer(help="C# 项目智能助手")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def version_callback(value: bool):
    """显示版本信息"""
    if value:
        from .. import __version__

        typer.echo(f"C# 助手版本: {__version__}")
        raise typer.Exit()


@app.callback()
def main(
    version: bool = typer.Option(
        None,
        "--version",
        "-v",
        help="显示版本信息",
        callback=version_callback,
        is_eager=True,
    )
):
    """C# 项目智能助手，提供代码分析、重构和优化功能。"""
    pass


@app.command()
def init(
    project_path: Path = typer.Argument(
        ".",
        exists=True,
        file_okay=False,
        dir_okay=True,
        writable=True,
        resolve_path=True,
        help="C# 项目路径",
    ),
    force: bool = typer.Option(False, "--force", "-f", help="强制重新初始化项目"),
):
    """初始化 C# 项目分析"""
    try:
        # 加载配置
        config = get_config()

        # 创建项目实例
        project = Project(
            name=project_path.name,
            path=str(project_path),
            solution_path=None,  # 可以自动查找 .sln 文件
        )

        # 初始化项目服务
        knowledge_graph_service = KnowledgeGraphService(project)
        omnisharp_client = OmniSharpClient()
        project_service = ProjectService(project, knowledge_graph_service, omnisharp_client)

        # 执行初始化
        typer.echo(f"正在初始化项目: {project_path}")
        result = asyncio.run(project_service.initialize_project(force=force))

        if result["success"]:
            typer.echo("✅ 项目初始化成功！")
            typer.echo(f"分析统计: {result['metrics']}")
        else:
            typer.echo(f"❌ 项目初始化失败: {result.get('error', '未知错误')}", err=True)
            raise typer.Exit(1)

    except Exception as e:
        logger.exception("初始化项目时出错")
        typer.echo(f"❌ 发生错误: {str(e)}", err=True)
        raise typer.Exit(1)


@app.command()
def analyze(
    project_path: Path = typer.Argument(
        ".",
        exists=True,
        file_okay=False,
        dir_okay=True,
        writable=True,
        resolve_path=True,
        help="C# 项目路径",
    ),
    output_format: str = typer.Option(
        "text", "--format", "-f", help="输出格式: text, json"
    ),
):
    """分析 C# 项目代码"""
    try:
        # 加载配置
        config = get_config()

        # 创建项目实例
        project = Project(
            name=project_path.name,
            path=str(project_path),
        )

        # 初始化项目服务
        knowledge_graph_service = KnowledgeGraphService(project)
        omnisharp_client = OmniSharpClient()
        project_service = ProjectService(project, knowledge_graph_service, omnisharp_client)

        # 执行分析
        typer.echo(f"正在分析项目: {project_path}")
        result = asyncio.run(project_service.analyze_project())

        # 输出结果
        if output_format.lower() == "json":
            import json

            typer.echo(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            # 文本格式输出
            typer.echo("\n📊 分析结果:")
            typer.echo(f"• 总文件数: {result.get('total_files', 0)}")
            typer.echo(f"• 类数量: {result.get('class_count', 0)}")
            typer.echo(f"• 方法数量: {result.get('method_count', 0)}")
            typer.echo(f"• 问题数: {len(result.get('issues', []))}")

            # 显示问题
            issues = result.get("issues", [])
            if issues:
                typer.echo("\n⚠️ 发现以下问题:")
                for issue in issues[:10]:  # 最多显示10个问题
                    typer.echo(
                        f"  • {issue['file']}:{issue['line']} - {issue['message']} ({issue['severity']})"
                    )

                if len(issues) > 10:
                    typer.echo(f"  ... 还有 {len(issues) - 10} 个问题未显示")
            else:
                typer.echo("\n✅ 未发现问题")

    except Exception as e:
        logger.exception("分析项目时出错")
        typer.echo(f"❌ 发生错误: {str(e)}", err=True)
        raise typer.Exit(1)


@app.command()
def search(
    query: str = typer.Argument(..., help="搜索查询"),
    project_path: Path = typer.Option(
        ".",
        "--project",
        "-p",
        exists=True,
        file_okay=False,
        dir_okay=True,
        resolve_path=True,
        help="C# 项目路径",
    ),
    limit: int = typer.Option(10, "--limit", "-l", help="返回结果数量限制"),
):
    """在项目中搜索代码"""
    try:
        # 加载配置
        config = get_config()

        # 创建项目实例
        project = Project(
            name=project_path.name,
            path=str(project_path),
        )

        # 初始化项目服务
        knowledge_graph_service = KnowledgeGraphService(project)
        omnisharp_client = OmniSharpClient()
        project_service = ProjectService(project, knowledge_graph_service, omnisharp_client)

        # 执行搜索
        typer.echo(f"正在搜索: {query}")
        results = asyncio.run(project_service.search_code(query, limit=limit))

        # 显示结果
        if not results:
            typer.echo("未找到匹配的结果")
            return

        typer.echo(f"\n🔍 找到 {len(results)} 个结果:")

        for i, result in enumerate(results, 1):
            typer.echo(f"\n{i}. {result['name']} ({result['type']})")
            typer.echo(f"   文件: {result['file']}:{result.get('line', '?')}")

            # 显示代码片段
            if "snippet" in result:
                typer.echo("   " + "   ".join(result["snippet"].splitlines(True)))

    except Exception as e:
        logger.exception("搜索代码时出错")
        typer.echo(f"❌ 发生错误: {str(e)}", err=True)
        raise typer.Exit(1)


def main():
    """CLI 主入口"""
    app()


if __name__ == "__main__":
    main()
