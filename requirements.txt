# Test framework
pytest==7.4.0
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.11.1

# Code quality
black==23.7.0
isort==5.12.0
flake8==6.1.0
mypy==1.5.0

# Documentation
sphinx==6.2.1
sphinx-rtd-theme==1.2.2
sphinx-autodoc-typehints==1.23.0

# Development tools
pre-commit==3.3.3
tox==4.6.4

# Test coverage
coverage==7.2.7
codecov==2.1.13

# Other tools
python-dotenv==1.0.0

# Core dependencies
python-dotenv>=0.19.0
click>=8.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
loguru>=0.5.0
typer>=0.9.0

# Language Server Protocol
python-lsp-server>=1.2.0
pyls-spyder>=0.4.0

# Knowledge Graph
python-arango>=7.0.0

# AI Integration
openai>=0.27.0

# Async Support
anyio>=3.0.0