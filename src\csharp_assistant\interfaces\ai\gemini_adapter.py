import logging
import json
from typing import Any, Dict, List, Optional
import google.generativeai as genai

from .base import AIService
from ...config.config import get_config

logger = logging.getLogger(__name__)


class GeminiAdapter(AIService):
    """Google Gemini AI 服务适配器"""

    def __init__(self):
        config = get_config()
        if not config.gemini_api_key:
            logger.warning("Gemini API密钥未配置")
            self.model = None
        else:
            genai.configure(api_key=config.gemini_api_key)
            self.model = genai.GenerativeModel(config.gemini_model)

    def _is_available(self) -> bool:
        """检查Gemini服务是否可用"""
        return self.model is not None

    async def analyze_code(self, code_analysis: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("使用 Gemini 分析代码结构")

        if not self._is_available():
            return {"analysis_result": "Gemini API密钥未配置，无法进行代码分析"}

        try:
            file_path = code_analysis.get('file_path', 'unknown')

            prompt = f"""你是一个专业的C#代码分析专家。请分析以下C#代码结构并提供详细报告：

文件路径: {file_path}
代码结构: {json.dumps(code_analysis, indent=2, ensure_ascii=False)}

请提供包含以下内容的分析报告：
1. 代码质量评估
2. 潜在问题识别
3. 改进建议
4. 设计模式识别

请用中文回答，保持专业和准确。"""

            response = self.model.generate_content(prompt)
            analysis_result = response.text

            return {
                "analysis_result": analysis_result,
                "file_path": file_path,
                "model_used": "gemini"
            }

        except Exception as e:
            logger.error(f"Gemini代码分析失败: {e}", exc_info=True)
            return {"analysis_result": f"Gemini代码分析失败: {str(e)}"}

    async def get_suggestions(self, context: Dict[str, Any], query: Optional[str] = None) -> List[str]:
        logger.info("使用 Gemini 获取代码建议")

        if not self._is_available():
            return ["Gemini API密钥未配置，无法提供建议"]

        try:
            prompt = f"""你是一个专业的C#开发专家。基于以下上下文信息和用户查询，提供实用的代码改进建议：

上下文: {json.dumps(context, indent=2, ensure_ascii=False)}
用户查询: {query or '请提供通用的代码改进建议'}

请提供3-5个具体的改进建议，每个建议应该具体、可操作，并符合C#最佳实践。请用中文回答。"""

            response = self.model.generate_content(prompt)
            suggestions_text = response.text

            # 简单解析建议
            suggestions = [s.strip() for s in suggestions_text.split('\n') if s.strip() and (s.strip().startswith(('1.', '2.', '3.', '4.', '5.', '-')))]

            return suggestions if suggestions else [suggestions_text]

        except Exception as e:
            logger.error(f"Gemini获取建议失败: {e}", exc_info=True)
            return [f"Gemini获取建议失败: {str(e)}"]

    async def report_error(self, error_info: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("使用 Gemini 报告错误并获取修复建议")

        if not self._is_available():
            return {"fix_suggestion": "Gemini API密钥未配置，无法提供错误修复建议"}

        try:
            prompt = f"""你是一个专业的C#错误诊断和修复专家。请分析以下错误并提供修复建议：

错误信息: {json.dumps(error_info, indent=2, ensure_ascii=False)}
代码上下文: {json.dumps(context, indent=2, ensure_ascii=False)}

请提供详细的错误分析和修复建议，包括：
1. 错误原因分析
2. 具体的修复步骤
3. 预防类似错误的建议

请用中文回答，保持专业和准确。"""

            response = self.model.generate_content(prompt)
            fix_suggestion = response.text

            return {
                "fix_suggestion": fix_suggestion,
                "confidence": "medium",
                "model_used": "gemini"
            }

        except Exception as e:
            logger.error(f"Gemini错误报告失败: {e}", exc_info=True)
            return {"fix_suggestion": f"Gemini错误分析失败: {str(e)}"}

    async def fix_error(self, error_info: Dict[str, Any], fix_suggestion: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("使用 Gemini 应用错误修复")

        if not self._is_available():
            return {"status": "failed", "message": "Gemini API密钥未配置，无法应用错误修复"}

        try:
            prompt = f"""你是一个专业的C#代码修复专家。基于以下信息生成具体的代码修复方案：

错误信息: {json.dumps(error_info, indent=2, ensure_ascii=False)}
修复建议: {json.dumps(fix_suggestion, indent=2, ensure_ascii=False)}
代码上下文: {json.dumps(context, indent=2, ensure_ascii=False)}

请提供具体的代码修复方案，包括：
1. 需要修改的具体代码行
2. 修改前后的代码对比
3. 修复验证步骤

请用中文回答，确保修复方案的准确性。"""

            response = self.model.generate_content(prompt)
            fix_plan = response.text

            return {
                "status": "success",
                "fix_plan": fix_plan,
                "model_used": "gemini"
            }

        except Exception as e:
            logger.error(f"Gemini错误修复失败: {e}", exc_info=True)
            return {"status": "failed", "message": f"Gemini错误修复失败: {str(e)}"}
