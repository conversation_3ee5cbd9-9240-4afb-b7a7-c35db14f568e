using System;

namespace TestProject
{
    public class TestClass
    {
        public string MyProperty { get; set; }
        private int _myField;

        public TestClass()
        {
            MyProperty = "Hello";
            _myField = 123;
        }

        public void MyMethod(string param1, int param2)
        {
            Console.WriteLine($"Param1: {param1}, Param2: {param2}");
        }

        private int Calculate(int a, int b)
        {
            return a + b;
        }
    }
}