"""
领域实体

定义核心业务实体。
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum, auto
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4


class EntityType(str, Enum):
    """实体类型枚举"""

    NAMESPACE = "namespace"
    CLASS = "class"
    INTERFACE = "interface"
    METHOD = "method"
    PROPERTY = "property"
    FIELD = "field"
    ENUM = "enum"
    STRUCT = "struct"
    DELEGATE = "delegate"
    EVENT = "event"


class AccessModifier(str, Enum):
    """访问修饰符"""

    PUBLIC = "public"
    PRIVATE = "private"
    PROTECTED = "protected"
    INTERNAL = "internal"
    PROTECTED_INTERNAL = "protected internal"
    PRIVATE_PROTECTED = "private protected"


@dataclass
class CodeEntity:
    """代码实体基类"""

    name: str
    full_name: str
    type: EntityType
    id: UUID = field(default_factory=uuid4)
    access_modifier: AccessModifier = AccessModifier.PRIVATE
    documentation: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

    def __post_init__(self):
        """初始化后处理"""
        self.updated_at = datetime.utcnow()


@dataclass
class Namespace(CodeEntity):
    """命名空间实体"""

    def __post_init__(self):
        """初始化后处理"""
        self.type = EntityType.NAMESPACE
        super().__post_init__()


@dataclass
class TypeDefinition(CodeEntity):
    """类型定义基类"""

    namespace: str = ""
    is_static: bool = False
    is_abstract: bool = False
    is_sealed: bool = False
    base_types: List[str] = field(default_factory=list)

    @property
    def is_inheritable(self) -> bool:
        """是否可被继承"""
        return not (self.is_sealed or self.is_static)


@dataclass
class ClassDefinition(TypeDefinition):
    """类定义"""
    methods: List["MethodDefinition"] = field(default_factory=list)
    properties: List["PropertyDefinition"] = field(default_factory=list)

    def __post_init__(self):
        """初始化后处理"""
        self.type = EntityType.CLASS
        super().__post_init__()


@dataclass
class InterfaceDefinition(TypeDefinition):
    """接口定义"""
    methods: List["MethodDefinition"] = field(default_factory=list)
    properties: List["PropertyDefinition"] = field(default_factory=list)

    def __post_init__(self):
        """初始化后处理"""
        self.type = EntityType.INTERFACE
        self.is_abstract = True  # 接口总是抽象的
        super().__post_init__()


@dataclass
class MethodDefinition(CodeEntity):
    """方法定义"""

    return_type: str = "void"
    is_async: bool = False
    is_extension: bool = False
    is_override: bool = False
    is_virtual: bool = False
    is_abstract: bool = False
    parameters: List[Dict[str, str]] = field(
        default_factory=list
    )  # [{"name": "param1", "type": "string"}, ...]

    def __post_init__(self):
        """初始化后处理"""
        self.type = EntityType.METHOD
        super().__post_init__()


@dataclass
class PropertyDefinition(CodeEntity):
    """属性定义"""

    property_type: str = "object"
    has_getter: bool = True
    has_setter: bool = False
    is_auto_property: bool = True

    def __post_init__(self):
        """初始化后处理"""
        self.type = EntityType.PROPERTY
        super().__post_init__()


@dataclass
class Project:
    """项目实体"""

    name: str
    path: str
    id: UUID = field(default_factory=uuid4)
    solution_path: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)

    def __post_init__(self):
        """初始化后处理"""
        self.updated_at = datetime.utcnow()


# 导出所有实体类
__all__ = [
    "EntityType",
    "AccessModifier",
    "CodeEntity",
    "Namespace",
    "TypeDefinition",
    "ClassDefinition",
    "InterfaceDefinition",
    "MethodDefinition",
    "PropertyDefinition",
    "Project",
]
