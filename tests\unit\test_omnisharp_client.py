"""
OmniSharp客户端单元测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path

from src.csharp_assistant.interfaces.language_server.omnisharp_client import OmniSharpClient


class TestOmniSharpClient:
    """OmniSharp客户端测试类"""

    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        config = MagicMock()
        config.language_server_enabled = True
        config.language_server_path = "/path/to/omnisharp"
        config.project_path = "/path/to/project"
        return config

    @pytest.fixture
    def omnisharp_client(self, mock_config):
        """创建OmniSharp客户端实例"""
        with patch('src.csharp_assistant.interfaces.language_server.omnisharp_client.get_config', return_value=mock_config):
            return OmniSharpClient()

    def test_init(self, omnisharp_client, mock_config):
        """测试初始化"""
        assert omnisharp_client.config == mock_config
        assert omnisharp_client.process is None
        assert omnisharp_client.reader is None
        assert omnisharp_client.writer is None
        assert omnisharp_client.request_id == 0
        assert not omnisharp_client.is_initialized

    def test_get_next_request_id(self, omnisharp_client):
        """测试请求ID生成"""
        assert omnisharp_client._get_next_request_id() == 1
        assert omnisharp_client._get_next_request_id() == 2
        assert omnisharp_client._get_next_request_id() == 3

    def test_path_to_uri_windows(self, omnisharp_client):
        """测试Windows路径转URI"""
        with patch('os.name', 'nt'):
            uri = omnisharp_client._path_to_uri("C:\\path\\to\\file.cs")
            assert uri.startswith("file:///")
            assert "C:" in uri

    def test_path_to_uri_unix(self, omnisharp_client):
        """测试Unix路径转URI"""
        with patch('os.name', 'posix'):
            with patch('pathlib.Path.resolve') as mock_resolve:
                mock_resolve.return_value = Path("/path/to/file.cs")
                uri = omnisharp_client._path_to_uri("/path/to/file.cs")
                assert uri.startswith("file://")

    @pytest.mark.asyncio
    async def test_start_disabled(self, omnisharp_client):
        """测试禁用语言服务器时的启动"""
        omnisharp_client.config.language_server_enabled = False
        await omnisharp_client.start("/test/path")
        assert omnisharp_client.process is None

    @pytest.mark.asyncio
    async def test_start_no_path(self, omnisharp_client):
        """测试未配置语言服务器路径时的启动"""
        omnisharp_client.config.language_server_path = None
        await omnisharp_client.start("/test/path")
        assert omnisharp_client.process is None

    @pytest.mark.asyncio
    async def test_analyze_code_not_initialized(self, omnisharp_client):
        """测试未初始化时的代码分析"""
        result = await omnisharp_client.analyze_code("/test/file.cs")
        assert result["classes"] == []
        assert result["methods"] == []
        assert result["fields"] == []
        assert result["diagnostics"] == []

    @pytest.mark.asyncio
    async def test_analyze_code_file_not_exists(self, omnisharp_client):
        """测试分析不存在的文件"""
        omnisharp_client.is_initialized = True
        
        with patch('pathlib.Path.exists', return_value=False):
            result = await omnisharp_client.analyze_code("/nonexistent/file.cs")
            assert result["classes"] == []
            assert result["methods"] == []
            assert result["fields"] == []
            assert result["diagnostics"] == []

    def test_extract_access_modifier(self, omnisharp_client):
        """测试访问修饰符提取"""
        assert omnisharp_client._extract_access_modifier("public class Test") == "public"
        assert omnisharp_client._extract_access_modifier("private void Method()") == "private"
        assert omnisharp_client._extract_access_modifier("protected string Field") == "protected"
        assert omnisharp_client._extract_access_modifier("internal class Internal") == "internal"
        assert omnisharp_client._extract_access_modifier("class Default") == "public"

    def test_extract_return_type(self, omnisharp_client):
        """测试返回类型提取"""
        assert omnisharp_client._extract_return_type("public string GetName()") == "string"
        assert omnisharp_client._extract_return_type("void DoSomething()") == "void"
        assert omnisharp_client._extract_return_type("public static int Calculate()") == "int"

    def test_extract_type(self, omnisharp_client):
        """测试类型提取"""
        assert omnisharp_client._extract_type("public string Name") == "string"
        assert omnisharp_client._extract_type("private int count") == "int"
        assert omnisharp_client._extract_type("static readonly List<string> items") == "List<string>"

    def test_parse_document_symbols_empty(self, omnisharp_client):
        """测试解析空符号响应"""
        classes, methods, fields = omnisharp_client._parse_document_symbols(None)
        assert classes == []
        assert methods == []
        assert fields == []

        classes, methods, fields = omnisharp_client._parse_document_symbols({"result": []})
        assert classes == []
        assert methods == []
        assert fields == []

    def test_parse_document_symbols_with_class(self, omnisharp_client):
        """测试解析包含类的符号响应"""
        response = {
            "result": [
                {
                    "kind": 5,  # Class
                    "name": "TestClass",
                    "detail": "public class TestClass",
                    "children": []
                }
            ]
        }
        
        classes, methods, fields = omnisharp_client._parse_document_symbols(response)
        assert len(classes) == 1
        assert classes[0]["name"] == "TestClass"
        assert classes[0]["access_modifier"] == "public"
        assert not classes[0]["is_static"]

    def test_parse_document_symbols_with_method(self, omnisharp_client):
        """测试解析包含方法的符号响应"""
        response = {
            "result": [
                {
                    "kind": 6,  # Method
                    "name": "TestMethod",
                    "detail": "public async Task<string> TestMethod()",
                    "children": []
                }
            ]
        }
        
        classes, methods, fields = omnisharp_client._parse_document_symbols(response)
        assert len(methods) == 1
        assert methods[0]["name"] == "TestMethod"
        assert methods[0]["access_modifier"] == "public"
        assert methods[0]["is_async"]

    @pytest.mark.asyncio
    async def test_get_diagnostics_not_initialized(self, omnisharp_client):
        """测试未初始化时获取诊断信息"""
        result = await omnisharp_client.get_diagnostics("/test/file.cs")
        assert result == []

    @pytest.mark.asyncio
    async def test_get_code_completions_not_initialized(self, omnisharp_client):
        """测试未初始化时获取代码补全"""
        result = await omnisharp_client.get_code_completions("/test/file.cs", 10, 5)
        assert result == []

    @pytest.mark.asyncio
    async def test_get_definitions_not_initialized(self, omnisharp_client):
        """测试未初始化时获取定义"""
        result = await omnisharp_client.get_definitions("/test/file.cs", 10, 5)
        assert result == []

    @pytest.mark.asyncio
    async def test_get_references_not_initialized(self, omnisharp_client):
        """测试未初始化时获取引用"""
        result = await omnisharp_client.get_references("/test/file.cs", 10, 5)
        assert result == []

    @pytest.mark.asyncio
    async def test_rename_symbol_not_initialized(self, omnisharp_client):
        """测试未初始化时重命名符号"""
        result = await omnisharp_client.rename_symbol("/test/file.cs", 10, 5, "NewName")
        assert not result["success"]
        assert "未初始化" in result["error"]
