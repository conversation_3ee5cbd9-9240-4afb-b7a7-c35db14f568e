"""
应用入口点
"""

import asyncio
import logging

from .config.logging_config import setup_logging
from .core.application import Application

logger = logging.getLogger(__name__)


async def main():
    """应用主函数"""
    setup_logging()
    logger.info("开始启动 C# 项目助手")

    try:
        app = Application()
        await app.start()
    except Exception as e:
        logger.error(f"应用运行时出现严重错误: {e}", exc_info=True)
    finally:
        logger.info("C# 项目助手已停止")


if __name__ == "__main__":
    asyncio.run(main())
