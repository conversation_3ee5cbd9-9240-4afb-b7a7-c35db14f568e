#!/usr/bin/env python3
"""
C# Assistant CLI

命令行界面，用于启动和管理C# Assistant MCP服务器。
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.csharp_assistant.mcp_server import MCPServer
from src.csharp_assistant.config.config import get_config

app = typer.Typer(
    name="csharp-assistant",
    help="C# Assistant - 智能C#代码分析和建议工具",
    add_completion=False
)
console = Console()


@app.command("start")
def start_server(
    project_path: Optional[str] = typer.Option(
        None,
        "--project-path", "-p",
        help="C#项目路径"
    ),
    project_name: Optional[str] = typer.Option(
        None,
        "--project-name", "-n", 
        help="项目名称"
    ),
    ai_provider: Optional[str] = typer.Option(
        None,
        "--ai-provider", "-a",
        help="AI服务提供商 (openai/gemini/ollama)"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose", "-v",
        help="启用详细日志输出"
    )
):
    """启动C# Assistant MCP服务器"""
    
    # 显示启动信息
    console.print(Panel.fit(
        Text("C# Assistant MCP Server", style="bold blue"),
        title="启动中...",
        border_style="blue"
    ))
    
    # 设置环境变量（如果提供了参数）
    import os
    if project_path:
        os.environ["PROJECT_PATH"] = project_path
    if project_name:
        os.environ["PROJECT_NAME"] = project_name
    if ai_provider:
        os.environ["AI_PROVIDER"] = ai_provider
    if verbose:
        os.environ["LOG_LEVEL"] = "DEBUG"
    
    # 显示配置信息
    config = get_config()
    console.print(f"项目路径: {config.project_path}")
    console.print(f"项目名称: {config.project_name}")
    console.print(f"AI提供商: {config.ai_provider}")
    console.print(f"语言服务器: {'启用' if config.language_server_enabled else '禁用'}")
    console.print()
    
    # 启动服务器
    async def run_server():
        server = MCPServer()
        await server.start()
    
    try:
        asyncio.run(run_server())
    except KeyboardInterrupt:
        console.print("\n[yellow]服务器已停止[/yellow]")
    except Exception as e:
        console.print(f"[red]启动失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("config")
def show_config():
    """显示当前配置"""
    config = get_config()
    
    console.print(Panel.fit(
        Text("当前配置", style="bold green"),
        border_style="green"
    ))
    
    config_info = f"""
项目路径: {config.project_path}
项目名称: {config.project_name}
AI提供商: {config.ai_provider}
语言服务器路径: {config.language_server_path}
语言服务器启用: {'是' if config.language_server_enabled else '否'}
MCP主机: {config.mcp_host}
MCP端口: {config.mcp_port}
数据库URL: {config.database_url}
"""
    
    console.print(config_info.strip())


@app.command("test")
def test_connection(
    component: str = typer.Argument(
        "all",
        help="要测试的组件 (all/database/ai/omnisharp)"
    )
):
    """测试各组件连接"""
    
    async def run_tests():
        from src.csharp_assistant.core.application import Application
        
        console.print(Panel.fit(
            Text("连接测试", style="bold yellow"),
            border_style="yellow"
        ))
        
        app = Application()
        
        if component in ["all", "database"]:
            console.print("测试数据库连接...")
            try:
                await app.knowledge_graph_manager.initialize_schema()
                console.print("[green]✓ 数据库连接正常[/green]")
            except Exception as e:
                console.print(f"[red]✗ 数据库连接失败: {e}[/red]")
        
        if component in ["all", "ai"]:
            console.print("测试AI服务连接...")
            try:
                ai_service = app.ai_manager.get_service()
                if ai_service:
                    # 简单测试
                    result = await ai_service.get_suggestions({}, "测试连接")
                    console.print("[green]✓ AI服务连接正常[/green]")
                else:
                    console.print("[yellow]! AI服务未配置[/yellow]")
            except Exception as e:
                console.print(f"[red]✗ AI服务连接失败: {e}[/red]")
        
        if component in ["all", "omnisharp"]:
            console.print("测试OmniSharp连接...")
            try:
                config = get_config()
                if config.language_server_enabled and config.language_server_path:
                    await app.omnisharp_client.start(config.project_path)
                    console.print("[green]✓ OmniSharp连接正常[/green]")
                    await app.omnisharp_client.stop()
                else:
                    console.print("[yellow]! OmniSharp未配置或已禁用[/yellow]")
            except Exception as e:
                console.print(f"[red]✗ OmniSharp连接失败: {e}[/red]")
    
    try:
        asyncio.run(run_tests())
    except Exception as e:
        console.print(f"[red]测试失败: {e}[/red]")
        raise typer.Exit(1)


@app.command("version")
def show_version():
    """显示版本信息"""
    console.print(Panel.fit(
        Text("C# Assistant v1.0.0", style="bold cyan"),
        subtitle="智能C#代码分析和建议工具",
        border_style="cyan"
    ))


def main():
    """CLI主入口点"""
    app()


if __name__ == "__main__":
    main()
