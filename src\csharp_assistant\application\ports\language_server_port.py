from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional

class LanguageServerPort(ABC):
    """语言服务器端口定义"""

    @abstractmethod
    async def start(self, project_path: str):
        """启动语言服务器"""
        pass

    @abstractmethod
    async def stop(self):
        """停止语言服务器"""
        pass

    @abstractmethod
    async def analyze_code(self, file_path: str) -> Dict[str, Any]:
        """分析代码文件"""
        pass

    @abstractmethod
    async def get_diagnostics(self, file_path: str) -> List[Dict[str, Any]]:
        """获取代码诊断信息（错误、警告等）"""
        pass

    @abstractmethod
    async def get_code_completions(self, file_path: str, line: int, character: int) -> List[str]:
        """获取代码补全建议"""
        pass

    @abstractmethod
    async def get_definitions(self, file_path: str, line: int, character: int) -> List[Dict[str, Any]]:
        """获取定义跳转信息"""
        pass

    @abstractmethod
    async def get_references(self, file_path: str, line: int, character: int) -> List[Dict[str, Any]]:
        """获取引用信息"""
        pass

    @abstractmethod
    async def rename_symbol(self, file_path: str, line: int, character: int, new_name: str) -> Dict[str, Any]:
        """重命名符号"""
        pass