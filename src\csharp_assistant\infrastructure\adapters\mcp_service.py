import asyncio
import logging
from typing import Any, Dict, List, Optional
import threading

from fastmcp import FastMCP
from pydantic import BaseModel # Keep BaseModel for internal data structures if needed

from ...config.config import get_config
from ...infrastructure.persistence.arangodb_repository import KnowledgeGraphManager
from ...interfaces.language_server.omnisharp_client import OmniSharpClient
from ...interfaces.ai.ai_manager import AIManager

logger = logging.getLogger(__name__)


class MCPService:
    """MCP 服务，提供工具接口"""

    def __init__(
        self,
        knowledge_graph_manager: KnowledgeGraphManager,
        omnisharp_client: OmniSharpClient,
        ai_manager: AIManager,
    ):
        self.config = get_config()
        self.mcp = FastMCP(name="CSharpAssistant") # Initialize FastMCP
        self.knowledge_graph_manager = knowledge_graph_manager
        self.omnisharp_client = omnisharp_client
        self.ai_manager = ai_manager
        self._register_tools()

    def _register_tools(self):
        # Register tools using @self.mcp.tool()
        @self.mcp.tool(name="initialize_project", description="Initializes a C# project and starts monitoring.")
        async def initialize_project(project_path: str, project_name: str):
            logger.info(f"初始化项目: {project_name} at {project_path}")
            # Here you would trigger the project analysis and knowledge graph update
            # For now, just a placeholder
            return {"message": "Project initialization triggered"}

        @self.mcp.tool(name="query_project", description="Queries the project knowledge graph for information.")
        async def query_project(project_name: str, question: str):
            logger.info(f"查询项目 {project_name}: {question}")
            context = await self.knowledge_graph_manager.query_context(question)
            ai_service = self.ai_manager.get_service()
            if not ai_service:
                # In a real scenario, FastMCP might handle errors differently or you'd raise a specific tool error
                return {"error": "AI service not available"}
            suggestions = await ai_service.get_suggestions(context={"question": question, "knowledge_graph_context": context}, query=question)
            return {"answer": suggestions}

        @self.mcp.tool(name="get_suggestions", description="Gets code suggestions based on context.")
        async def get_suggestions(project_name: str, context: str):
            logger.info(f"获取建议 for {project_name} with context: {context}")
            ai_service = self.ai_manager.get_service()
            if not ai_service:
                return {"error": "AI service not available"}
            suggestions = await ai_service.get_suggestions(context={"user_context": context})
            return {"suggestions": suggestions}

        @self.mcp.tool(name="report_error", description="Reports an error and gets a fix suggestion.")
        async def report_error(project_name: str, error_info: Dict[str, Any]):
            logger.info(f"报告错误 for {project_name}: {error_info}")
            ai_service = self.ai_manager.get_service()
            if not ai_service:
                return {"error": "AI service not available"}
            fix_suggestion = await ai_service.report_error(error_info=error_info, context={})
            return {"fix_suggestion": fix_suggestion}

        @self.mcp.tool(name="fix_error", description="Applies an error fix.")
        async def fix_error(project_name: str, error_id: str, fix_result: Dict[str, Any]):
            logger.info(f"修复错误 for {project_name}, error_id: {error_id}")
            ai_service = self.ai_manager.get_service()
            if not ai_service:
                return {"error": "AI service not available"}
            result = await ai_service.fix_error(error_info={"error_id": error_id}, fix_suggestion=fix_result, context={})
            return {"status": "fix_applied", "result": result}

    async def start(self):
        """启动 MCP 服务"""
        host = self.config.mcp_host
        port = self.config.mcp_port
        logger.info(f"正在启动 MCP 服务，监听地址: http://{host}:{port}")
        await self.mcp.start(host=host, port=port)

    async def stop(self):
        """停止 MCP 服务"""
        logger.info("正在停止 MCP 服务")
        await self.mcp.stop()
        if self.mcp_thread and self.mcp_thread.is_alive():
            self.mcp_thread.join(timeout=5) # Wait for the thread to finish
        logger.info("MCP 服务已停止")
