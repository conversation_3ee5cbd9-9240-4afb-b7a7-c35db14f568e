"""
知识图谱服务

提供知识图谱相关的核心业务逻辑。
"""

import logging
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

from ...infrastructure.persistence import KnowledgeGraphRepository
from ...core.domain.entities import Project, ClassDefinition, MethodDefinition, CodeEntity, PropertyDefinition
from ...core.domain.value_objects import CodeAnalysisResult, CodeChange

logger = logging.getLogger(__name__)


class KnowledgeGraphService:
    """知识图谱服务"""

    def __init__(self, project: Project):
        """
        初始化知识图谱服务

        Args:
            project: 项目实体
        """
        self.project = project
        self.knowledge_graph_repo: KnowledgeGraphRepository = KnowledgeGraphRepository()

    async def initialize_database(self) -> None:
        """
        初始化数据库

        创建必要的集合和图。
        """
        try:
            await self.knowledge_graph_repo.initialize_schema()
        except Exception as e:
            logger.warning(f"初始化知识图谱数据库时出错: {e}")
            raise

    async def update_from_analysis(self, analysis_result: CodeAnalysisResult) -> None:
        """
        从分析结果更新知识图谱

        Args:
            analysis_result: 代码分析结果
        """
        try:
            await self.knowledge_graph_repo.update_from_analysis(self.project.name, analysis_result)
            logger.info(f"知识图谱更新完成，共处理 {len(analysis_result.dependencies)} 个依赖关系")

        except Exception as e:
            logger.error(f"更新知识图谱时出错: {e}", exc_info=True)
            raise

    def _entity_to_doc(self, entity: CodeEntity) -> Dict[str, Any]:
        """
        将 CodeEntity 转换为 ArangoDB 文档
        """
        doc_data = {
            "_key": str(entity.id),
            "name": entity.name,
            "full_name": entity.full_name,
            "type": entity.type.value,
            "access_modifier": entity.access_modifier.value
            if isinstance(entity.access_modifier, Enum)
            else entity.access_modifier,
            "documentation": entity.documentation,
            "metadata": entity.metadata,
            "created_at": entity.created_at.isoformat(),
            "updated_at": entity.updated_at.isoformat(),
        }

        if isinstance(entity, ClassDefinition):
            doc_data.update(
                {
                    "namespace": entity.namespace,
                    "is_static": entity.is_static,
                    "is_abstract": entity.is_abstract,
                    "is_sealed": entity.is_sealed,
                    "base_types": entity.base_types,
                }
            )
        elif isinstance(entity, MethodDefinition):
            doc_data.update(
                {
                    "return_type": entity.return_type,
                    "is_async": entity.is_async,
                    "is_extension": entity.is_extension,
                    "is_override": entity.is_override,
                    "is_virtual": entity.is_virtual,
                    "is_abstract": entity.is_abstract,
                    "parameters": entity.parameters,
                }
            )
        elif isinstance(entity, PropertyDefinition):
            doc_data.update(
                {
                    "property_type": entity.property_type,
                    "has_getter": entity.has_getter,
                    "has_setter": entity.has_setter,
                    "is_auto_property": entity.is_auto_property,
                }
            )

        return doc_data

    def _get_entity_key(self, full_name: str) -> Optional[str]:
        """
        根据 full_name 获取实体文档键 (_key)
        """
        aql = "FOR d IN code_entities FILTER d.full_name == @full_name RETURN d._key"
        cursor = self.knowledge_graph_repo.db.aql.execute(aql, bind_vars={"full_name": full_name})
        return cursor.next()  # Returns None if not found

    async def find_related_entities(
        self, entity_full_name: str, max_depth: int = 2
    ) -> Dict[str, Any]:
        """
        查找相关实体

        Args:
            entity_full_name: 实体全名
            max_depth: 最大搜索深度

        Returns:
            Dict[str, Any]: 相关实体信息
        """
        try:
            aql = f"""
            FOR v, e, p IN 1..{max_depth} ANY SHORTEST_PATH 'code_entities/{self._get_entity_key(entity_full_name)}' TO 'code_entities' GRAPH 'knowledge_graph'
            RETURN {{nodes: UNIQUE(p.vertices), edges: UNIQUE(p.edges)}}
            """

            cursor = self.knowledge_graph_repo.db.aql.execute(aql)

            nodes = set()
            relationships = set()

            async for record in cursor:
                for node_doc in record["nodes"]:
                    nodes.add(
                        (
                            node_doc.get("full_name", ""),
                            node_doc.get("type", "unknown"),
                            node_doc.get("name", ""),
                        )
                    )
                for edge_doc in record["edges"]:
                    relationships.add(
                        (edge_doc["_from"], edge_doc["_to"], edge_doc["type"])
                    )

            return {
                "entity": entity_full_name,
                "related_nodes": [
                    {"full_name": n[0], "type": n[1], "name": n[2]} for n in nodes
                ],
                "relationships": [
                    {"from": r[0], "to": r[1], "type": r[2]} for r in relationships
                ],
            }

        except Exception as e:
            logger.error(f"查找相关实体时出错: {e}", exc_info=True)
            return {
                "entity": entity_full_name,
                "error": str(e),
                "related_nodes": [],
                "relationships": [],
            }

    async def get_entity_impact(self, entity_full_name: str) -> Dict[str, Any]:
        """
        获取实体影响分析

        Args:
            entity_full_name: 实体全名

        Returns:
            Dict[str, Any]: 影响分析结果
        """
        try:
            entity_key = self._get_entity_key(entity_full_name)
            if not entity_key:
                return {
                    "entity": entity_full_name,
                    "dependents": [],
                    "total_dependents": 0,
                }

            aql = f"""
            FOR v, e, p IN 1..3 INBOUND 'code_entities/{entity_key}' GRAPH 'knowledge_graph'
            RETURN DISTINCT {{dependent: v, depth: LENGTH(p.edges)}}
            """

            cursor = self.knowledge_graph_repo.db.aql.execute(aql)

            dependents = []
            async for record in cursor:
                node = record["dependent"]
                dependents.append(
                    {
                        "full_name": node["full_name"],
                        "name": node.get("name", ""),
                        "type": node.get("type", ""),
                        "depth": record["depth"],
                    }
                )

            return {
                "entity": entity_full_name,
                "dependents": dependents,
                "total_dependents": len(dependents),
            }

        except Exception as e:
            logger.error(f"获取实体影响分析时出错: {e}", exc_info=True)
            return {
                "entity": entity_full_name,
                "error": str(e),
                "dependents": [],
                "total_dependents": 0,
            }

    async def visualize_entity_relationships(
        self, entity_full_name: str, max_depth: int = 2
    ) -> Dict[str, Any]:
        """
        可视化实体关系

        Args:
            entity_full_name: 实体全名
            max_depth: 最大深度

        Returns:
            Dict[str, Any]: 可视化数据
        """
        try:
            entity_key = self._get_entity_key(entity_full_name)
            if not entity_key:
                return {
                    "nodes": [],
                    "links": [],
                    "error": f"Entity {entity_full_name} not found.",
                }

            aql = f"""
            FOR v, e, p IN 1..{max_depth} ANY 'code_entities/{entity_key}' GRAPH 'knowledge_graph'
            RETURN {{nodes: UNIQUE(p.vertices), edges: UNIQUE(p.edges)}}
            """

            cursor = self.knowledge_graph_repo.db.aql.execute(aql)

            nodes = set()
            links = set()

            async for record in cursor:
                for node_doc in record["nodes"]:
                    nodes.add((node_doc["_key"], node_doc.get("type", "unknown")))
                for edge_doc in record["edges"]:
                    links.add((edge_doc["_from"], edge_doc["_to"], edge_doc["type"]))

            # 转换为前端可用的格式
            node_list = [{"id": n[0], "type": n[1]} for n in nodes]
            link_list = [{"source": l[0], "target": l[1], "type": l[2]} for l in links]

            return {"nodes": node_list, "links": link_list}

        except Exception as e:
            logger.error(f"可视化实体关系时出错: {e}", exc_info=True)
            return {"nodes": [], "links": [], "error": str(e)}
