import typer
import asyncio
import httpx
import logging
from typing import Optional

from ..config.config import get_config

logger = logging.getLogger(__name__)

app = typer.Typer()

@app.command()
async def init(
    project_path: str = typer.Option(..., help="Path to the C# project solution file."),
    project_name: str = typer.Option(..., help="Name of the C# project."),
):
    """Initializes the C# project and starts monitoring."""
    config = get_config()
    mcp_host = config.mcp_host
    mcp_port = config.mcp_port
    url = f"http://{mcp_host}:{mcp_port}/api/projects/initialize"
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json={
                "project_path": project_path,
                "project_name": project_name
            })
            response.raise_for_status()  # Raise an exception for HTTP errors
            typer.echo(f"Project initialization response: {response.json()}")
    except httpx.RequestError as exc:
        typer.echo(f"An error occurred while requesting {exc.request.url!r}: {exc}")
    except httpx.HTTPStatusError as exc:
        typer.echo(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text}")

@app.command()
async def query(
    project_name: str = typer.Option(..., help="Name of the C# project."),
    question: str = typer.Option(..., help="Question about the project."),
):
    """Queries the project knowledge graph."""
    config = get_config()
    mcp_host = config.mcp_host
    mcp_port = config.mcp_port
    url = f"http://{mcp_host}:{mcp_port}/api/query"

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json={
                "project_name": project_name,
                "question": question
            })
            response.raise_for_status()
            typer.echo(f"Query response: {response.json()}")
    except httpx.RequestError as exc:
        typer.echo(f"An error occurred while requesting {exc.request.url!r}: {exc}")
    except httpx.HTTPStatusError as exc:
        typer.echo(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text}")

@app.command()
async def suggest(
    project_name: str = typer.Option(..., help="Name of the C# project."),
    context: str = typer.Option(..., help="Context for the suggestion."),
):
    """Gets code suggestions based on context."""
    config = get_config()
    mcp_host = config.mcp_host
    mcp_port = config.mcp_port
    url = f"http://{mcp_host}:{mcp_port}/api/suggestions"

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json={
                "project_name": project_name,
                "context": context
            })
            response.raise_for_status()
            typer.echo(f"Suggestion response: {response.json()}")
    except httpx.RequestError as exc:
        typer.echo(f"An error occurred while requesting {exc.request.url!r}: {exc}")
    except httpx.HTTPStatusError as exc:
        typer.echo(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text}")

@app.command(name="report-error")
async def report_error_cmd(
    project_name: str = typer.Option(..., help="Name of the C# project."),
    error_info: str = typer.Option(..., help="JSON string of error information."),
):
    """Reports an error and gets a fix suggestion."""
    config = get_config()
    mcp_host = config.mcp_host
    mcp_port = config.mcp_port
    url = f"http://{mcp_host}:{mcp_port}/api/errors/report"

    try:
        import json
        error_info_dict = json.loads(error_info)
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json={
                "project_name": project_name,
                "error_info": error_info_dict
            })
            response.raise_for_status()
            typer.echo(f"Report error response: {response.json()}")
    except json.JSONDecodeError:
        typer.echo("Error: error_info must be a valid JSON string.")
    except httpx.RequestError as exc:
        typer.echo(f"An error occurred while requesting {exc.request.url!r}: {exc}")
    except httpx.HTTPStatusError as exc:
        typer.echo(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text}")

@app.command(name="fix-error")
async def fix_error_cmd(
    project_name: str = typer.Option(..., help="Name of the C# project."),
    error_id: str = typer.Option(..., help="ID of the error to fix."),
    fix_result: str = typer.Option(..., help="JSON string of fix result information."),
):
    """Applies an error fix."""
    config = get_config()
    mcp_host = config.mcp_host
    mcp_port = config.mcp_port
    url = f"http://{mcp_host}:{mcp_port}/api/errors/fix"

    try:
        import json
        fix_result_dict = json.loads(fix_result)
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json={
                "project_name": project_name,
                "error_id": error_id,
                "fix_result": fix_result_dict
            })
            response.raise_for_status()
            typer.echo(f"Fix error response: {response.json()}")
    except json.JSONDecodeError:
        typer.echo("Error: fix_result must be a valid JSON string.")
    except httpx.RequestError as exc:
        typer.echo(f"An error occurred while requesting {exc.request.url!r}: {exc}")
    except httpx.HTTPStatusError as exc:
        typer.echo(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text}")


def main():
    asyncio.run(app())


if __name__ == "__main__":
    main()
