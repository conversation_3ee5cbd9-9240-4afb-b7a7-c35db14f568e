# C# 项目智能助手

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue)](https://www.python.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Imports: isort](https://img.shields.io/badge/%20imports-isort-%231674b1?style=flat&labelColor=ef8336)](https://pycqa.github.io/isort/)

一个基于 AI 的 C# 项目智能开发助手，提供代码分析、智能建议和自动化重构功能。

## ✨ 功能特点

- **智能代码分析**：深入解析 C# 项目结构和依赖关系
- **代码质量检查**：识别潜在问题和优化点
- **智能重构建议**：提供代码重构建议
- **知识图谱**：构建项目知识图谱，可视化代码关系
- **AI 辅助**：集成 AI 模型，提供智能开发建议
- **命令行工具**：简单易用的命令行界面
- **可扩展架构**：模块化设计，易于扩展新功能

## 🚀 快速开始

### 安装

1. 使用 pip 安装（推荐）：
   ```bash
   pip install csharp-assistant
   ```

2. 或者从源码安装：
   ```bash
   # 克隆仓库
   git clone https://github.com/yourusername/csharp-assistant.git
   cd csharp-assistant
   
   # 安装开发版本
   pip install -e .
   ```

### 配置

1. 创建配置文件：
   ```bash
   # 复制示例配置文件
   cp .env.example .env
   ```

2. 编辑 `.env` 文件，配置你的项目路径和其他设置：
   ```ini
   # 项目配置
   PROJECT_NAME=YourProjectName
   PROJECT_PATH=/path/to/your/csharp/project
   
   # Neo4j 配置
   NEO4J_URI=bolt://localhost:7687
   NEO4J_USER=neo4j
   NEO4J_PASSWORD=your_password
   
   # OmniSharp 语言服务器路径
   LANGUAGE_SERVER_PATH=/path/to/omnisharp/OmniSharp.exe
   
   # 日志级别
   LOG_LEVEL=INFO
   ```

### 使用

```bash
# 初始化项目分析
csharp-assistant init /path/to/your/csharp/project

# 分析项目代码
csharp-assistant analyze /path/to/your/csharp/project

# 搜索代码
csharp-assistant search "class name" --project /path/to/your/csharp/project

# 查看帮助
csharp-assistant --help
```

## 🏗️ 项目结构

```
csharp-assistant/
├── src/
│   └── csharp_assistant/     # 源代码
│       ├── cli/              # 命令行界面
│       ├── config/           # 配置管理
│       ├── core/             # 核心领域逻辑
│       ├── interfaces/       # 接口适配层
│       └── infrastructure/   # 基础设施层
│           └── persistence/  # 数据持久化
├── tests/                   # 测试代码
│   ├── integration/         # 集成测试
│   └── unit/                # 单元测试
├── .env.example             # 环境变量示例
├── requirements.txt         # 项目依赖
├── requirements-dev.txt     # 开发依赖
├── setup.py                # 打包配置
├── pytest.ini              # Pytest 配置
└── README.md               # 项目说明
```

## 🛠️ 开发

请参考 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何设置开发环境和贡献代码。

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit

# 运行集成测试
pytest tests/integration

# 生成测试覆盖率报告
pytest --cov=src --cov-report=html
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！请确保：

1. 阅读 [CONTRIBUTING.md](CONTRIBUTING.md) 指南
2. 编写清晰的提交信息
3. 添加或更新相关测试
4. 更新相关文档

## 📄 许可证

[MIT](LICENSE) © 2023 Your Name
