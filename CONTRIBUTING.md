# 贡献指南

欢迎参与 C# 项目智能助手的开发！本指南将帮助你快速上手项目开发。

## 开发环境搭建

1. **克隆仓库**
   ```bash
   git clone https://github.com/yourusername/csharp-assistant.git
   cd csharp-assistant
   ```

2. **创建并激活虚拟环境**
   ```bash
   # Windows
   python -m venv venv
   .\venv\Scripts\activate
   
   # macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **安装开发依赖**
   ```bash
   pip install -r requirements-dev.txt
   ```

4. **安装项目**
   ```bash
   pip install -e .
   ```

## 代码风格

我们使用以下工具来保持代码风格一致：

- **代码格式化**: Black
  ```bash
  black src tests
  ```

- **导入排序**: isort
  ```bash
  isort src tests
  ```

- **代码检查**: flake8
  ```bash
  flake8 src tests
  ```

- **类型检查**: mypy
  ```bash
  mypy src
  ```

## 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit -v

# 运行集成测试
pytest tests/integration -v

# 生成测试覆盖率报告
pytest --cov=src --cov-report=html
```

### 编写测试

- 单元测试放在 `tests/unit` 目录下
- 集成测试放在 `tests/integration` 目录下
- 测试文件名以 `test_` 开头
- 测试类名以 `Test` 开头
- 测试方法名以 `test_` 开头

## 提交代码

1. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add your feature"
   ```

3. **推送到远程仓库**
   ```bash
   git push origin feature/your-feature-name
   ```

4. **创建 Pull Request**
   - 访问 GitHub 仓库
   - 点击 "New Pull Request"
   - 选择你的功能分支
   - 填写 PR 描述
   - 等待代码审查

## 提交信息规范

我们遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat: 添加用户登录功能
fix: 修复用户注册时的空指针异常
docs: 更新 README 文件
```

## 代码审查

- 所有代码提交都需要经过代码审查
- 确保代码符合项目规范
- 确保所有测试通过
- 确保添加了必要的文档

## 问题报告

如果你发现任何问题，请提交 issue：

1. 检查是否已有相关 issue
2. 如果没有，请创建新 issue
3. 提供详细的复现步骤
4. 包括环境信息
5. 添加相关日志或截图

## 许可证

本项目采用 [MIT 许可证](LICENSE)。

---

感谢你的贡献！🎉
