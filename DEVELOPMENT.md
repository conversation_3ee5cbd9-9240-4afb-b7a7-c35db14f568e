# C# 项目助手开发文档

## 目录
1. [项目概述](#项目概述)
2. [系统架构](#系统架构)
3. [环境配置](#环境配置)
4. [模块化设计](#模块化设计)
5. [核心组件](#核心组件)
6. [API 接口](#api-接口)
7. [开发指南](#开发指南)
8. [部署说明](#部署说明)
9. [测试](#测试)
10. [常见问题](#常见问题)

## 项目概述

C# 项目助手是一个智能开发辅助工具，它通过实时监控 C# 项目文件变化，利用语言服务器进行精确代码分析，构建知识图谱，并集成大语言模型提供智能建议和错误修复功能。

## 系统架构

### 高层架构

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  C# 语言服务器   |<--->|   Python 助手    |<--->|   知识图谱数据库  |
|  (OmniSharp)     |     |  (核心业务逻辑)  |     |   (ArangoDB)       |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
                               ^      ^
                               |      |
                               v      v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|   文件系统监控   |     |   AI 分析引擎    |     |   MCP 服务接口  |
|  (Watchdog)      |     |  (LLM 集成)     |     |   (预留接口)     |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
```

### 模块化架构

```
+--------------------------------------------------------------------+
|                        csharp-assistant                            |
+--------------------------------------------------------------------+
|  +----------------+     +------------------+     +---------------+ |
|  |                |     |                  |     |               | |
|  |  核心领域层     |<--->|   应用服务层     |<--->|   接口适配层   | |
|  |  (core)        |     |  (application)   |     | (interfaces)  | |
|  |                |     |                  |     |               | |
|  +----------------+     +------------------+     +-------+-------+ |
|          ^                         ^                      |         |
|          |                         |                      v         |
|  +----------------+     +------------------+     +-------+-------+ |
|  |   基础设施层    |     |   配置管理      |     |   外部依赖     | |
|  | (infrastructure)|     |   (config)      |     | (AI, 数据库等) | |
|  |                |     |                  |     |               | |
|  +----------------+     +------------------+     +---------------+ |
+--------------------------------------------------------------------+
```

## 环境配置

### 1. 创建虚拟环境

```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
.\venv\Scripts\activate  # Windows
```

### 2. 安装依赖

项目使用分层依赖管理，安装基础依赖：

```bash
pip install -r requirements/base.txt


```

### 3. 环境变量配置

创建 `.env` 文件：

```ini
# 项目配置
PROJECT_NAME=MyCSharpProject
PROJECT_PATH=C:\path\to\your\solution.sln
LOG_LEVEL=INFO

# 语言服务器配置
LANGUAGE_SERVER_PATH=C:\path\to\omnisharp\OmniSharp.exe

# ArangoDB 配置
ARANGODB_URI=bolt://localhost:7687
ARANGODB_USER=ArangoDB
ARANGODB_PASSWORD=your-password

# AI 配置
AI_DEFAULT_PROVIDER=openai  # openai, gemini, ollama

# OpenAI 配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# Google Gemini 配置
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-pro

# Ollama 配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3

# MCP 服务配置
MCP_HOST=0.0.0.0
MCP_PORT=8000
```

## 模块化设计

### 项目结构

```
csharp-assistant/
├── .env.example             # 环境变量示例
├── pyproject.toml           # 项目元数据和依赖
├── requirements/            # 分层依赖管理
│   ├── base.txt             # 基础依赖
├── src/
│   └── csharp_assistant/    # 主包
│       ├── __init__.py
│       ├── main.py          # 应用入口点
│       │
│       ├── config/          # 配置管理
│       │   ├── __init__.py
│       │   ├── settings.py  # 应用设置
│       │   └── logging_config.py  # 日志配置
│       │
│       ├── core/            # 核心领域层
│       │   ├── __init__.py
│       │   ├── application.py  # 应用核心
│       │   └── domain/      # 领域模型
│       │       ├── __init__.py
│       │       ├── entities.py  # 领域实体
│       │       └── value_objects.py  # 值对象
│       │
│       ├── interfaces/      # 接口适配层
│       │   ├── __init__.py
│       │   ├── ai/          # AI 接口
│       │   │   ├── __init__.py
│       │   │   ├── base.py  # AI 适配器接口
│       │   │   ├── openai_adapter.py
│       │   │   ├── gemini_adapter.py
│       │   │   └── ollama_adapter.py
│       │   │
│       │   ├── database/    # 数据库接口
│       │   │   ├── __init__.py
│       │   │   └── graph_repository.py
│       │   │
│       │   └── language_server/  # 语言服务器接口
│       │       ├── __init__.py
│       │       └── omnisharp_client.py
│       │
│       ├── application/     # 应用服务层
│       │   ├── __init__.py
│       │   ├── use_cases/   # 用例实现
│       │   │   ├── __init__.py
│       │   │   ├── initialize_project.py
│       │   │   ├── query_knowledge.py
│       │   │   ├── get_suggestions.py
│       │   │   └── handle_errors.py
│       │   │
│       │   └── ports/       # 端口定义
│       │       ├── __init__.py
│       │       ├── ai_port.py
│       │       ├── repository_port.py
│       │       └── language_server_port.py
│       │
│       └── infrastructure/  # 基础设施层
│           ├── __init__.py
│           ├── adapters/    # 适配器实现
│           │   ├── __init__.py
│           │   ├── file_watcher.py
│           │   └── mcp_service.py
│           │
│   └── persistence/  # 持久化实现
│
├── tests/                   # 测试目录
│   ├── __init__.py
│   ├── conftest.py         # 测试配置
│   ├── unit/               # 单元测试
│   │   ├── __init__.py
│   │   └── test_*.py
│   └── integration/        # 集成测试
│       ├── __init__.py
│       └── test_*.py
│
└── scripts/                # 实用脚本
    ├── setup_environment.sh
    └── start_services.sh
```

### 模块职责

1. **核心领域层 (core/)**
   - 包含领域模型和业务规则
   - 不依赖外部框架或库
   - 定义实体、值对象和领域服务

2. **应用服务层 (application/)**
   - 实现具体用例
   - 协调领域对象执行业务逻辑
   - 定义端口接口

3. **接口适配层 (interfaces/)**
   - 定义外部系统接口
   - 包括 AI、数据库和语言服务器的接口

4. **基础设施层 (infrastructure/)**
   - 实现接口适配层定义的接口
   - 处理外部依赖和框架集成
   - 包含文件系统、网络等基础设施代码

5. **配置管理 (config/)**
   - 集中管理应用配置
   - 处理环境变量和配置文件
   - 配置日志系统

## 核心组件

### 1. 配置管理 (config.py)

```python
import os
from dotenv import load_dotenv
from typing import Dict, Any

class Config:
    """配置管理类"""
    
    def __init__(self):
        # 加载 .env 文件
        load_dotenv()
        
        # 项目配置
        self.project_name = os.getenv("PROJECT_NAME", "MyCSharpProject")
        self.project_path = os.getenv("PROJECT_PATH")
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        
        # 语言服务器配置
        self.language_server_path = os.getenv("LANGUAGE_SERVER_PATH")
        
        
        
        # AI 配置
        self.ai_default_provider = os.getenv("AI_DEFAULT_PROVIDER", "openai")
        
        # OpenAI 配置
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.openai_model = os.getenv("OPENAI_MODEL", "gpt-4")
        
        # Google Gemini 配置
        self.gemini_api_key = os.getenv("GEMINI_API_KEY")
        self.gemini_model = os.getenv("GEMINI_MODEL", "gemini-pro")
        
        # Ollama 配置
        self.ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        self.ollama_model = os.getenv("OLLAMA_MODEL", "llama3")
        
        # MCP 服务配置
        self.mcp_host = os.getenv("MCP_HOST", "0.0.0.0")
        self.mcp_port = int(os.getenv("MCP_PORT", "8000"))
    
    def validate(self) -> bool:
        """验证配置是否有效"""
        required_vars = {
            "PROJECT_PATH": self.project_path,
            "LANGUAGE_SERVER_PATH": self.language_server_path,
            "NEO4J_URI": self.neo4j_uri,
            "NEO4J_USER": self.neo4j_user,
            "NEO4J_PASSWORD": self.neo4j_password
        }
        
        missing = [var for var, value in required_vars.items() if not value]
        if missing:
            raise ValueError(f"Missing required environment variables: {', '.join(missing)}")
            
        return True

# 全局配置实例
config = Config()
```

### 2. AI 服务基类 (ai/base.py)

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import json

class AIService(ABC):
    """AI 服务抽象基类"""
    
    @abstractmethod
    async def analyze_code(
        self, 
        code_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析代码结构"""
        pass
        
    @abstractmethod
    async def get_suggestions(
        self, 
        context: Dict[str, Any],
        query: Optional[str] = None
    ) -> List[str]:
        """获取代码建议"""
        pass
        
    @abstractmethod
    async def report_error(
        self,
        error_info: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """报告错误并获取修复建议"""
        pass
        
    @abstractmethod
    async def fix_error(
        self,
        error_info: Dict[str, Any],
        fix_suggestion: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """应用错误修复"""
        pass
```

### 3. 知识图谱服务 (knowledge_graph.py)

```python
from typing import Dict, List, Any, Optional
from arango import ArangoClient
from src.csharp_assistant.application.ports.repository_port import KnowledgeGraphPort
from src.csharp_assistant.config.config import get_config


class KnowledgeGraphManager(KnowledgeGraphPort):
    """知识图谱管理器"""

    def __init__(self):
        config = get_config()
        self.client = ArangoClient(hosts=f"http://{config.arangodb_host}:{config.arangodb_port}")
        self.db = self.client.db(
            config.arangodb_database,
            username=config.arangodb_user,
            password=config.arangodb_password,
        )
        self.graph = self.db.graph("csharp_knowledge_graph")

    async def initialize_schema(self):
        """初始化知识图谱模式"""
        # Create vertex collections
        for collection_name in ["Project", "File", "Class", "Method", "Parameter", "Field"]:
            if not self.db.has_collection(collection_name):
                self.db.create_collection(collection_name)

        # Create edge collections and define graph
        edge_definitions = [
            {"edge_collection": "CONTAINS_FILE", "from_vertex_collections": ["Project"], "to_vertex_collections": ["File"]},
            {"edge_collection": "CONTAINS_CLASS", "from_vertex_collections": ["File"], "to_vertex_collections": ["Class"]},
            {"edge_collection": "CONTAINS_METHOD", "from_vertex_collections": ["Class"], "to_vertex_collections": ["Method"]},
            {"edge_collection": "HAS_PARAMETER", "from_vertex_collections": ["Method"], "to_vertex_collections": ["Parameter"]},
            {"edge_collection": "CALLS", "from_vertex_collections": ["Method"], "to_vertex_collections": ["Method"]},
            {"edge_collection": "HAS_FIELD", "from_vertex_collections": ["Class"], "to_vertex_collections": ["Field"]},
        ]
        
        if not self.db.has_graph("csharp_knowledge_graph"):
            self.db.create_graph("csharp_knowledge_graph", edge_definitions=edge_definitions)
        else:
            # Update edge definitions if graph already exists
            existing_graph = self.db.graph("csharp_knowledge_graph")
            for edge_def in edge_definitions:
                if not existing_graph.has_edge_definition(edge_def["edge_collection"]):
                    existing_graph.create_edge_definition(**edge_def)

        # Create indexes
        # For Project, File, Class, Method, Field, Parameter, we'll use hash indexes on 'name' or 'path'
        # For full-text search, ArangoDB uses ArangoSearch Views.
        # This is a basic example, a real implementation would involve more complex AQL and view definitions.
        # For simplicity, we'll rely on direct AQL queries for now.
        
        # Ensure persistent indexes for common lookups
        for col_name, field_name in [
            ("Project", "name"), ("File", "path"), ("Class", "name"), 
            ("Method", "full_name"), ("Field", "name"), ("Parameter", "name")
        ]:
            collection = self.db.collection(col_name)
            if not any(idx["type"] == "persistent" and field_name in idx["fields"] for idx in collection.indexes()):
                collection.add_index(type='hash', fields=[field_name], unique=False)


    async def update_from_analysis(
        self, 
        project_name: str,
        analysis: Dict[str, Any]
    ):
        """根据代码分析结果更新知识图谱"""
        # Get collections
        projects_col = self.db.collection("Project")
        files_col = self.db.collection("File")
        classes_col = self.db.collection("Class")
        methods_col = self.db.collection("Method")
        parameters_col = self.db.collection("Parameter")
        fields_col = self.db.collection("Field")

        # Get edge collections
        contains_file_edge = self.graph.edge_collection("CONTAINS_FILE")
        contains_class_edge = self.graph.edge_collection("CONTAINS_CLASS")
        contains_method_edge = self.graph.edge_collection("CONTAINS_METHOD")
        has_parameter_edge = self.graph.edge_collection("HAS_PARAMETER")
        calls_edge = self.graph.edge_collection("CALLS")
        has_field_edge = self.graph.edge_collection("HAS_FIELD")

        # Create or update Project node
        project_key = project_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_") # Simple key generation
        try:
            project_doc = projects_col.insert({"_key": project_key, "name": project_name}, overwrite=True)
        except Exception as e:
            print(f"Error inserting project: {e}")
            return

        for file_data in analysis.get("files", []):
            file_path = file_data.get("path")
            if not file_path:
                continue

            file_key = file_path.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
            file_doc = files_col.insert({"_key": file_key, "path": file_path}, overwrite=True)
            contains_file_edge.insert({"_from": project_doc["_id"], "_to": file_doc["_id"]}, overwrite=True)

            for class_data in file_data.get("classes", []):
                class_name = class_data.get("name")
                class_namespace = class_data.get("namespace")
                if not class_name:
                    continue

                class_full_name = f"{class_namespace}.{class_name}" if class_namespace else class_name
                class_key = class_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                class_doc = classes_col.insert({"_key": class_key, "name": class_name, "namespace": class_namespace, "full_name": class_full_name}, overwrite=True)
                contains_class_edge.insert({"_from": file_doc["_id"], "_to": class_doc["_id"]}, overwrite=True)

                for method_data in class_data.get("methods", []):
                    method_name = method_data.get("name")
                    method_return_type = method_data.get("return_type")
                    if not method_name:
                        continue

                    method_full_name = f"{class_full_name}.{method_name}"
                    method_key = method_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                    method_doc = methods_col.insert({"_key": method_key, "name": method_name, "return_type": method_return_type, "full_name": method_full_name}, overwrite=True)
                    contains_method_edge.insert({"_from": class_doc["_id"], "_to": method_doc["_id"]}, overwrite=True)

                    for param_data in method_data.get("parameters", []):
                        param_name = param_data.get("name")
                        param_type = param_data.get("type")
                        if not param_name:
                            continue
                        param_key = f"{method_full_name}.{param_name}".replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                        param_doc = parameters_col.insert({"_key": param_key, "name": param_name, "type": param_type}, overwrite=True)
                        has_parameter_edge.insert({"_from": method_doc["_id"], "_to": param_doc["_id"]}, overwrite=True)

                    for called_method_full_name in method_data.get("calls", []):
                        # Find or create callee method
                        callee_key = called_method_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                        callee_doc = methods_col.insert({"_key": callee_key, "full_name": called_method_full_name}, overwrite=True)
                        calls_edge.insert({"_from": method_doc["_id"], "_to": callee_doc["_id"]}, overwrite=True)

                for field_data in class_data.get("fields", []):
                    field_name = field_data.get("name")
                    field_type = field_data.get("type")
                    if not field_name:
                        continue

                    field_full_name = f"{class_full_name}.{field_name}"
                    field_key = field_full_name.replace("/", "_").replace("\\", "_").replace(":", "_").replace(".", "_")
                    field_doc = fields_col.insert({"_key": field_key, "name": field_name, "type": field_type}, overwrite=True)
                    has_field_edge.insert({"_from": class_doc["_id"], "_to": field_doc["_id"]}, overwrite=True)

    async def query_context(
        self, 
        query: str,
        limit: int = 5
    ) -> List[Dict]:
        """查询相关知识图谱上下文"""
        # This is a simplified example. For robust full-text search, ArangoSearch Views are recommended.
        # Here, we'll do a basic AQL search on 'name' and 'full_name' fields.
        aql_query = f"""
        FOR node IN @@collection
            FILTER LIKE(LOWER(node.name), LOWER(@query_param), true) OR LIKE(LOWER(node.full_name), LOWER(@query_param), true)
            LIMIT @limit_param
            RETURN node
        """
        
        results = []
        # Search in Class and Method collections
        for collection_name in ["Class", "Method"]:
            cursor = self.db.aql.execute(
                aql_query,
                bind_vars={
"@collection": collection_name, "query_param": f"%{query}%", "limit_param": limit}
            )
            results.extend([doc for doc in cursor])
        
        return results

    def close(self):
        """关闭数据库连接"""
        # ArangoDB client doesn't require explicit close for HTTP connections
        pass
```
```

## API 接口

以下是 MCP 服务将提供的五个核心接口（预留实现）：

1. **初始化项目**
   - 路径: `/api/projects/initialize`
   - 方法: POST
   - 参数: `{ "project_path": "string", "project_name": "string" }`
   - 描述: 初始化项目并开始监控

2. **查询**
   - 路径: `/api/query`
   - 方法: POST
   - 参数: `{ "project_name": "string", "question": "string" }`
   - 描述: 查询项目知识

3. **获取建议**
   - 路径: `/api/suggestions`
   - 方法: POST
   - 参数: `{ "project_name": "string", "context": "string" }`
   - 描述: 获取代码改进建议

4. **报告错误**
   - 路径: `/api/errors/report`
   - 方法: POST
   - 参数: `{ "project_name": "string", "error_info": { ... } }`
   - 描述: 报告错误并获取修复建议

5. **修复错误完成**
   - 路径: `/api/errors/fix`
   - 方法: POST
   - 参数: `{ "project_name": "string", "error_id": "string", "fix_result": { ... } }`
   - 描述: 确认错误修复完成

## 开发指南

### 1. 添加新的 AI 提供者

1. 在 `app/services/ai/` 目录下创建新文件，例如 `my_ai.py`
2. 实现 `AIService` 抽象基类
3. 在 `AIManager` 类中添加对新提供者的支持

### 2. 扩展知识图谱

1. 修改 `KnowledgeGraphManager` 类中的模式定义
2. 更新 `update_from_analysis` 方法以处理新的节点和关系类型

### 3. 运行测试

```bash
pytest tests/
```

## 部署说明

### 1. 系统要求

- Python 3.10+
- ArangoDB 3.x+
- .NET SDK 6.0+ (用于运行 OmniSharp)
- 至少 8GB 内存

### 2. 部署步骤

1. 安装依赖: `pip install -r requirements.txt`
2. 配置 `.env` 文件
3. 启动 ArangoDB 服务
4. 启动应用程序: `python -m app.main`

## 测试

### 单元测试

```bash
pytest tests/unit/
```

### 集成测试

```bash
pytest tests/integration/
```

### 端到端测试

```bash
pytest tests/e2e/
```

## 常见问题

### 1. 语言服务器连接失败

**问题**: 无法连接到 OmniSharp 语言服务器  
**解决方案**:
- 检查 `LANGUAGE_SERVER_PATH` 配置是否正确
- 确保 OmniSharp 已正确安装
- 检查防火墙设置

### 2. ArangoDB 连接问题

**问题**: 无法连接到 ArangoDB 数据库  
**解决方案**:
- 检查 ArangoDB 服务是否正在运行
- 验证 `ARANGODB_HOST`、`ARANGODB_PORT`、`ARANGODB_USER`、`ARANGODB_PASSWORD` 和 `ARANGODB_DATABASE` 配置


### 3. AI 服务 API 密钥无效

**问题**: AI 服务返回认证错误  
**解决方案**:
- 检查相应的 API 密钥是否有效
- 确保 API 密钥有足够的配额
- 验证 API 端点是否可访问

### 4. 文件监控不工作

**问题**: 文件更改未触发分析  
**解决方案**:
- 检查 `PROJECT_PATH` 配置是否正确
- 确保应用程序有权限访问项目目录
- 检查日志以获取更多信息
