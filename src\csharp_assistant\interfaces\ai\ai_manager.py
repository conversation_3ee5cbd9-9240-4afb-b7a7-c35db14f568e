import logging
from typing import Any, Dict, List, Optional

from .base import AIService
from .openai_adapter import OpenAIAdapter
from .gemini_adapter import GeminiAdapter
from .ollama_adapter import OllamaAdapter
from ...config.config import get_config

logger = logging.getLogger(__name__)


class AIManager:
    """AI 服务管理器，根据配置动态加载 AI 适配器"""

    def __init__(self):
        self.config = get_config()
        self.ai_service: Optional[AIService] = None
        self._initialize_ai_service()

    def _initialize_ai_service(self):
        provider = self.config.llm_provider.lower()
        if provider == "openai":
            self.ai_service = OpenAIAdapter()
        elif provider == "gemini":
            self.ai_service = GeminiAdapter()
        elif provider == "ollama":
            self.ai_service = OllamaAdapter()
        else:
            logger.warning(f"不支持的 AI 提供者: {provider}，AI 服务将不可用")
            self.ai_service = None

    def get_service(self) -> Optional[AIService]:
        """获取当前配置的 AI 服务实例"""
        if self.ai_service is None:
            logger.error("AI 服务未初始化或配置错误")
        return self.ai_service
