"""
Ollama适配器单元测试
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
import httpx

from src.csharp_assistant.interfaces.ai.ollama_adapter import OllamaAdapter


class TestOllamaAdapter:
    """Ollama适配器测试类"""

    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        config = MagicMock()
        config.ollama_base_url = "http://localhost:11434"
        config.ollama_model = "qwen2.5:3b"
        return config

    @pytest.fixture
    def ollama_adapter(self, mock_config):
        """创建Ollama适配器实例"""
        with patch('src.csharp_assistant.interfaces.ai.ollama_adapter.get_config', return_value=mock_config):
            return OllamaAdapter()

    def test_init(self, ollama_adapter, mock_config):
        """测试初始化"""
        assert ollama_adapter.base_url == mock_config.ollama_base_url
        assert ollama_adapter.model == mock_config.ollama_model
        assert ollama_adapter.client is not None

    @pytest.mark.asyncio
    async def test_check_model_availability_success(self, ollama_adapter):
        """测试模型可用性检查成功"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "models": [
                {"name": "qwen2.5:3b"},
                {"name": "llama3:8b"}
            ]
        }
        
        with patch.object(ollama_adapter.client, 'get', return_value=mock_response) as mock_get:
            mock_get.return_value.raise_for_status = MagicMock()
            result = await ollama_adapter._check_model_availability()
            assert result is True

    @pytest.mark.asyncio
    async def test_check_model_availability_model_not_found(self, ollama_adapter):
        """测试模型不可用"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "models": [
                {"name": "llama3:8b"}
            ]
        }
        
        with patch.object(ollama_adapter.client, 'get', return_value=mock_response) as mock_get:
            mock_get.return_value.raise_for_status = MagicMock()
            result = await ollama_adapter._check_model_availability()
            assert result is False

    @pytest.mark.asyncio
    async def test_check_model_availability_error(self, ollama_adapter):
        """测试模型可用性检查出错"""
        with patch.object(ollama_adapter.client, 'get', side_effect=httpx.HTTPError("Connection error")):
            result = await ollama_adapter._check_model_availability()
            assert result is False

    @pytest.mark.asyncio
    async def test_generate_response_success(self, ollama_adapter):
        """测试成功生成响应"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "message": {
                "content": "这是AI生成的响应"
            }
        }
        
        with patch.object(ollama_adapter.client, 'post', return_value=mock_response) as mock_post:
            mock_post.return_value.raise_for_status = MagicMock()
            result = await ollama_adapter._generate_response("测试提示")
            assert result == "这是AI生成的响应"

    @pytest.mark.asyncio
    async def test_generate_response_timeout(self, ollama_adapter):
        """测试请求超时"""
        with patch.object(ollama_adapter.client, 'post', side_effect=httpx.TimeoutException("Timeout")):
            result = await ollama_adapter._generate_response("测试提示")
            assert "超时" in result

    @pytest.mark.asyncio
    async def test_generate_response_http_error(self, ollama_adapter):
        """测试HTTP错误"""
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        
        with patch.object(ollama_adapter.client, 'post', side_effect=httpx.HTTPStatusError("Error", request=None, response=mock_response)):
            result = await ollama_adapter._generate_response("测试提示")
            assert "AI服务错误" in result

    @pytest.mark.asyncio
    async def test_analyze_code_model_unavailable(self, ollama_adapter):
        """测试模型不可用时的代码分析"""
        with patch.object(ollama_adapter, '_check_model_availability', return_value=False):
            result = await ollama_adapter.analyze_code({"file_path": "test.cs"})
            assert "模型不可用" in result["analysis_result"]

    @pytest.mark.asyncio
    async def test_analyze_code_success(self, ollama_adapter):
        """测试成功的代码分析"""
        code_analysis = {
            "file_path": "test.cs",
            "classes": [{"name": "TestClass"}],
            "methods": [{"name": "TestMethod"}],
            "fields": [{"name": "testField"}]
        }
        
        with patch.object(ollama_adapter, '_check_model_availability', return_value=True):
            with patch.object(ollama_adapter, '_generate_response', return_value="详细的代码分析报告"):
                result = await ollama_adapter.analyze_code(code_analysis)
                assert result["analysis_result"] == "详细的代码分析报告"
                assert result["file_path"] == "test.cs"

    @pytest.mark.asyncio
    async def test_get_suggestions_model_unavailable(self, ollama_adapter):
        """测试模型不可用时获取建议"""
        with patch.object(ollama_adapter, '_check_model_availability', return_value=False):
            result = await ollama_adapter.get_suggestions({}, "测试查询")
            assert len(result) == 1
            assert "模型不可用" in result[0]

    @pytest.mark.asyncio
    async def test_get_suggestions_success(self, ollama_adapter):
        """测试成功获取建议"""
        context = {"project_name": "TestProject"}
        suggestions_text = """1. 使用更好的命名约定
2. 添加异常处理
3. 实现单元测试"""
        
        with patch.object(ollama_adapter, '_check_model_availability', return_value=True):
            with patch.object(ollama_adapter, '_generate_response', return_value=suggestions_text):
                result = await ollama_adapter.get_suggestions(context, "改进建议")
                assert len(result) == 3
                assert "命名约定" in result[0]
                assert "异常处理" in result[1]
                assert "单元测试" in result[2]

    @pytest.mark.asyncio
    async def test_report_error_model_unavailable(self, ollama_adapter):
        """测试模型不可用时报告错误"""
        with patch.object(ollama_adapter, '_check_model_availability', return_value=False):
            result = await ollama_adapter.report_error({}, {})
            assert "模型不可用" in result["fix_suggestion"]

    @pytest.mark.asyncio
    async def test_report_error_success(self, ollama_adapter):
        """测试成功报告错误"""
        error_info = {
            "type": "compilation_error",
            "message": "CS0103: The name 'variable' does not exist",
            "file": "test.cs",
            "line": 10
        }
        
        with patch.object(ollama_adapter, '_check_model_availability', return_value=True):
            with patch.object(ollama_adapter, '_generate_response', return_value="错误修复建议"):
                result = await ollama_adapter.report_error(error_info, {})
                assert result["fix_suggestion"] == "错误修复建议"
                assert result["error_type"] == "compilation_error"

    @pytest.mark.asyncio
    async def test_fix_error_model_unavailable(self, ollama_adapter):
        """测试模型不可用时修复错误"""
        with patch.object(ollama_adapter, '_check_model_availability', return_value=False):
            result = await ollama_adapter.fix_error({}, {}, {})
            assert result["status"] == "failed"
            assert "模型不可用" in result["message"]

    @pytest.mark.asyncio
    async def test_fix_error_success(self, ollama_adapter):
        """测试成功修复错误"""
        error_info = {"message": "变量未定义"}
        fix_suggestion = {"fix_suggestion": "声明变量"}
        
        with patch.object(ollama_adapter, '_check_model_availability', return_value=True):
            with patch.object(ollama_adapter, '_generate_response', return_value="具体修复方案"):
                result = await ollama_adapter.fix_error(error_info, fix_suggestion, {})
                assert result["status"] == "success"
                assert result["fix_plan"] == "具体修复方案"

    @pytest.mark.asyncio
    async def test_close(self, ollama_adapter):
        """测试关闭客户端"""
        mock_client = AsyncMock()
        ollama_adapter.client = mock_client
        
        await ollama_adapter.close()
        mock_client.aclose.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_code_exception(self, ollama_adapter):
        """测试代码分析异常处理"""
        with patch.object(ollama_adapter, '_check_model_availability', side_effect=Exception("测试异常")):
            result = await ollama_adapter.analyze_code({"file_path": "test.cs"})
            assert "代码分析失败" in result["analysis_result"]

    @pytest.mark.asyncio
    async def test_get_suggestions_exception(self, ollama_adapter):
        """测试获取建议异常处理"""
        with patch.object(ollama_adapter, '_check_model_availability', side_effect=Exception("测试异常")):
            result = await ollama_adapter.get_suggestions({}, "测试")
            assert len(result) == 1
            assert "获取建议失败" in result[0]
