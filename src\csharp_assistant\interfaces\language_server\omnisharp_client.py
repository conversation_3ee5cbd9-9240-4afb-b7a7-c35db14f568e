import asyncio
import logging
from typing import Any, Dict, List, Optional
from pathlib import Path

from ...application.ports.language_server_port import LanguageServerPort
from ...config.config import get_config

logger = logging.getLogger(__name__)


class OmniSharpClient(LanguageServerPort):
    """OmniSharp 语言服务器客户端"""

    def __init__(self):
        self.config = get_config()
        self.process = None
        self.reader = None
        self.writer = None

    async def start(self, project_path: str):
        """启动 OmniSharp 语言服务器"""
        if not self.config.language_server_enabled:
            logger.info("语言服务器已禁用，跳过启动")
            return

        if not self.config.language_server_path:
            logger.error("未配置 LANGUAGE_SERVER_PATH，无法启动 OmniSharp")
            return

        logger.info(f"正在启动 OmniSharp 语言服务器: {self.config.language_server_path}")
        try:
            self.process = await asyncio.create_subprocess_exec(
                self.config.language_server_path,
                "-s", project_path, # Pass project path to OmniSharp
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            # Add a small delay to allow pipes to be set up
            await asyncio.sleep(0.1) # Small delay
            if self.process.returncode is not None:
                logger.error(f"OmniSharp process exited immediately with code: {self.process.returncode}")
                return # Exit if process already exited

            self.reader = self.process.stdout
            self.writer = self.process.stdin
            logger.info("OmniSharp 语言服务器已启动")

            # Send initialize request
            initialize_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "processId": self.process.pid,
                    "rootUri": project_path,
                    "capabilities": {},
                },
            }
            await self._send_request(initialize_request)

            # Send initialized notification
            initialized_notification = {
                "jsonrpc": "2.0",
                "method": "initialized",
                "params": {},
            }
            await self._send_notification(initialized_notification)

        except FileNotFoundError:
            logger.error(f"找不到 OmniSharp 可执行文件: {self.config.language_server_path}")
        except Exception as e:
            logger.error(f"启动 OmniSharp 语言服务器失败: {e}", exc_info=True)

    async def _send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送 LSP 请求并等待响应"""
        import json

        message = json.dumps(request).encode("utf-8")
        content_length = len(message)
        headers = f"Content-Length: {content_length}\r\nContent-Type: application/json\r\n\r\n".encode("utf-8")

        self.writer.write(headers + message)
        await self.writer.drain()

        # Read response headers
        response_headers = {}
        while True:
            line = await self.reader.readline()
            if not line.strip():
                break
            key, value = line.decode("utf-8").strip().split(": ", 1)
            response_headers[key.lower()] = value

        content_length = int(response_headers.get("content-length", 0))
        if content_length == 0:
            return {}

        # Read response body
        response_body = await self.reader.readexactly(content_length)
        return json.loads(response_body.decode("utf-8"))

    async def _send_notification(self, notification: Dict[str, Any]) -> None:
        """发送 LSP 通知"""
        import json

        message = json.dumps(notification).encode("utf-8")
        content_length = len(message)
        headers = f"Content-Length: {content_length}\r\nContent-Type: application/json\r\n\r\n".encode("utf-8")

        self.writer.write(headers + message)
        await self.writer.drain()

    async def stop(self):
        """停止 OmniSharp 语言服务器"""
        if self.process and self.process.returncode is None:
            logger.info("正在停止 OmniSharp 语言服务器")
            self.process.terminate()
            await self.process.wait()
            logger.info("OmniSharp 语言服务器已停止")

    async def analyze_code(self, file_path: str) -> Dict[str, Any]:
        """分析代码文件"""
        logger.info(f"分析代码文件: {file_path}")

        # Send textDocument/didOpen notification
        did_open_notification = {
            "jsonrpc": "2.0",
            "method": "textDocument/didOpen",
            "params": {
                "textDocument": {
                    "uri": file_path,
                    "languageId": "csharp",
                    "version": 1,
                    "text": Path(file_path).read_text(),
                }
            },
        }
        await self._send_notification(did_open_notification)

        # Send textDocument/documentSymbol request
        document_symbol_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "textDocument/documentSymbol",
            "params": {
                "textDocument": {"uri": file_path}
            },
        }
        response = await self._send_request(document_symbol_request)

        # Parse response and return in CodeAnalysisResult format
        classes = []
        methods = []
        fields = []
        diagnostics = []

        def parse_symbols(symbols: List[Dict[str, Any]], container_name: str = ""):
            for symbol in symbols:
                kind = symbol.get("kind")
                name = symbol.get("name")
                current_full_name = f"{container_name}.{name}" if container_name else name

                if kind == 5:  # Class
                    classes.append({
                        "name": name,
                        "full_name": current_full_name,
                        "namespace": container_name,
                        "access_modifier": "public", # Placeholder
                        "is_static": False, # Placeholder
                        "is_abstract": False, # Placeholder
                        "is_sealed": False, # Placeholder
                        "base_types": [], # Placeholder
                        "methods": [],
                        "fields": [],
                        "detail": symbol.get("detail", ""),
                    })
                elif kind == 6:  # Method
                    methods.append({
                        "name": name,
                        "full_name": current_full_name,
                        "return_type": symbol.get("detail", "").split(' ')[0] if symbol.get("detail") else "void", # Extract return type from detail
                        "access_modifier": "public", # Placeholder
                        "is_async": False, # Placeholder
                        "is_extension": False, # Placeholder
                        "is_override": False, # Placeholder
                        "is_virtual": False, # Placeholder
                        "is_abstract": False, # Placeholder
                        "parameters": [], # Placeholder
                        "calls": [], # Placeholder for method calls,
                        "detail": symbol.get("detail", ""),
                    })
                elif kind == 7:  # Property
                    fields.append({
                        "name": name,
                        "full_name": current_full_name,
                        "type": symbol.get("detail", "").split(' ')[0] if symbol.get("detail") else "object", # Extract type from detail
                        "access_modifier": "public", # Placeholder
                        "has_getter": True, # Placeholder
                        "has_setter": True, # Placeholder
                        "is_auto_property": True, # Placeholder
                        "detail": symbol.get("detail", ""),
                    })
                elif kind == 8:  # Field
                    fields.append({
                        "name": name,
                        "full_name": current_full_name,
                        "type": symbol.get("detail", "").split(' ')[0] if symbol.get("detail") else "object", # Extract type from detail
                        "access_modifier": "public", # Placeholder
                        "has_getter": False, # Placeholder
                        "has_setter": False, # Placeholder
                        "is_auto_property": False, # Placeholder
                        "detail": symbol.get("detail", ""),
                    })

                if "children" in symbol:
                    parse_symbols(symbol["children"], current_full_name)

        if response and "result" in response:
            parse_symbols(response["result"])

        # Placeholder for diagnostics
        diagnostics.append({
            "type": "info",
            "message": f"Analysis completed for {file_path}",
            "file": file_path,
            "line": 1,
            "column": 1,
            "severity": "info",
        })

        return {"classes": classes, "methods": methods, "fields": fields, "diagnostics": diagnostics}

    async def get_diagnostics(self, file_path: str) -> List[Dict[str, Any]]:
        """获取代码诊断信息 (占位符实现)"""
        logger.info(f"获取诊断信息: {file_path}")
        return []

    async def get_code_completions(self, file_path: str, line: int, character: int) -> List[str]:
        """获取代码补全建议 (占位符实现)"""
        logger.info(f"获取代码补全: {file_path} at {line}:{character}")
        return ["placeholder_completion"]

    async def get_definitions(self, file_path: str, line: int, character: int) -> List[Dict[str, Any]]:
        """获取定义跳转信息 (占位符实现)"""
        logger.info(f"获取定义: {file_path} at {line}:{character}")
        return []

    async def get_references(self, file_path: str, line: int, character: int) -> List[Dict[str, Any]]:
        """获取引用信息 (占位符实现)"""
        logger.info(f"获取引用: {file_path} at {line}:{character}")
        return []

    async def rename_symbol(self, file_path: str, line: int, character: int, new_name: str) -> Dict[str, Any]:
        """重命名符号 (占位符实现)"""
        logger.info(f"重命名符号: {file_path} at {line}:{character} to {new_name}")
        return {"success": True, "changes": {}}
